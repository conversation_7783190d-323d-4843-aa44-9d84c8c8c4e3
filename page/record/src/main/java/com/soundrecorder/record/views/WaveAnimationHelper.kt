/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: WaveAnimationHelper
 * Description: animation create
 * Version: 1.0
 * Date: 2025/8/11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>          2025/8/11      1.0     animation create
 *********************************************************************************/
package com.soundrecorder.record.views

import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.record.R
import com.soundrecorder.record.RecorderActivity
import com.soundrecorder.record.views.wave.RecorderWaveRecyclerView
import com.soundrecorder.wavemark.wave.view.WaveItemView
import com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
import kotlin.math.floor

class WaveAnimationHelper(val activity: RecorderActivity, var isRealTimeSwitch: Boolean = false) {

    companion object {
        const val TAG = "WaveAnimationHelper"
        const val ANIMATION_TYPE_HEIGHT = 1
        const val ANIMATION_TYPE_MARGIN_TOP = 2
        const val ANIMATION_TYPE_SIZE = 3
        const val ANIMATION_TYPE_ALPHA = 4
        const val ANIMATION_BOUNDS = 0f
        const val ANIMATION_RESPONSE = 0.35f
        const val HEIGHT_PERCENT = 0.35f
    }

    var waveGradientIsVisible: Int = View.VISIBLE
    var realTimeView: LinearLayout? = null
    var waveGradientView: WaveViewGradientLayout? = null
    private var rulerView: RecorderWaveRecyclerView? = null
    private var recorderTop: LinearLayout? = null
    private var timerView: TextView? = null
    private var rootView: ConstraintLayout? = null
    private var translationBtn: TextView? = null
    private var toolbar: COUIToolbar? = null
    private var mTranscriptionTv: TextView? = null
    private val animationList: ArrayList<AnimationItem> = ArrayList()
    private var waveHeightLarge = activity.resources.displayMetrics.heightPixels * HEIGHT_PERCENT
    private var waveHeightSmall = activity.resources.getDimension(com.soundrecorder.common.R.dimen.dp106)
    private var waveMarginTopLarge = 0f
    private var waveMarginTopSmall = 0f
    private val timerViewSizeSmall = activity.baseContext.px2dp(activity.resources.getDimension(com.soundrecorder.common.R.dimen.px37).toInt())
    private val timerViewSizeLarge = activity.baseContext.px2dp(activity.resources.getDimension(com.soundrecorder.common.R.dimen.px48).toInt())
    private val recorderTopSmall = activity.resources.getDimension(com.soundrecorder.common.R.dimen.recorder_time_margin_top_small)
    private val recorderTopLarge = activity.resources.getDimension(com.soundrecorder.record.R.dimen.recorder_margin_top)
    private val toolbarList = IntArray(2)

    /**
     * 检查是否处于分屏或浮窗模式
     */
    private fun isInMultiWindowOrFloatingMode(): Boolean {
        return activity.isInMultiWindowMode()
    }

    init {
        realTimeView = activity.findViewById(com.soundrecorder.record.R.id.real_time_view)
        waveGradientView = activity.findViewById(com.soundrecorder.record.R.id.wave_gradient_view)
        rulerView = activity.findViewById(com.soundrecorder.record.R.id.ruler_view)
        recorderTop = activity.findViewById(com.soundrecorder.record.R.id.recorder_top)
        timerView = activity.findViewById(com.soundrecorder.record.R.id.timerView)
        rootView = activity.findViewById(com.soundrecorder.record.R.id.root_view)
        translationBtn = activity.findViewById(com.soundrecorder.record.R.id.transcription_tv)
        toolbar = activity.findViewById(com.soundrecorder.record.R.id.toolbar)
        mTranscriptionTv = activity.findViewById(R.id.transcription_tv)
        initViewAndAnimation()
        rulerView?.notifyDataSetChanged()
    }

    fun startAnimation() {
        if (isInMultiWindowOrFloatingMode()) {
            initViewAndAnimation()
            return
        }
        if (!animationIsRunning()) {
            animationList.forEach { animationItem ->
                animationItem.animation.apply {
                    spring.setFinalPosition(animationItem.startValue)
                    start()
                }
                val startValue = animationItem.startValue
                animationItem.startValue = animationItem.finalPosition
                animationItem.finalPosition = startValue
            }
        } else {
            mTranscriptionTv?.isEnabled = false
            timerView?.let {
                it.pivotX = (it.width / 2).toFloat()
            }
            animationList.forEach { animationItem ->
                animationItem.view.visibility = View.VISIBLE
                animationItem.animation.start()
            }
        }
    }

    fun animationIsRunning(): Boolean {
        animationList.forEach { animationItem ->
            if (animationItem.animation.isRunning) {
                return false
            }
        }
        return true
    }

    private fun initViewAndAnimation() {
        toolbar?.getLocationOnScreen(toolbarList)
        waveMarginTopLarge = activity.resources.getDimension(com.soundrecorder.record.R.dimen.wave_margin_top)
        val timeLinesHeight = activity.resources.getDimension(com.soundrecorder.common.R.dimen.px26)
        waveMarginTopSmall = activity.resources.getDimension(com.soundrecorder.common.R.dimen.recorder_wave_margin_top_small) - timeLinesHeight
        animationList.clear()
        waveGradientView?.visibility = waveGradientIsVisible
        mTranscriptionTv?.isEnabled = true
        if (isRealTimeSwitch) {
            createAnimationUp()
        } else {
            createAnimationDown()
        }
    }

    private fun createAnimationDown() {
        realTimeView?.let {
            it.translationY = 0f
            it.visibility = View.GONE
            it.alpha = 0f
            createAnimation(it, COUISpringAnimation.ALPHA, 0f, 1f, ANIMATION_RESPONSE, ANIMATION_BOUNDS)
            createConstrainAnimation(it, waveMarginTopLarge, waveMarginTopSmall, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_MARGIN_TOP)
        }

        timerView?.let {
            it.translationY = 0f
            it.scaleY = 1f
            it.scaleX = 1f
            it.pivotX = (it.width / 2).toFloat()
            it.pivotY = 0f
            it.textSize = floor(timerViewSizeLarge)
            createConstrainAnimation(it, timerViewSizeLarge, timerViewSizeSmall, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_SIZE)
        }

        recorderTop?.let {
            it.translationY = 0f
            val recorderTopParams = it.layoutParams as? MarginLayoutParams
            recorderTopParams?.topMargin = floor(recorderTopLarge).toInt()
            it.layoutParams = recorderTopParams
            createConstrainAnimation(it, recorderTopLarge, recorderTopSmall, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_MARGIN_TOP)
        }

        waveGradientView?.let {
            it.translationY = 0f
            val waveLayoutParams = waveGradientView?.layoutParams as? MarginLayoutParams
            waveLayoutParams?.height = floor(waveHeightLarge).toInt()
            waveLayoutParams?.topMargin = floor(waveMarginTopLarge).toInt()
            waveGradientView?.layoutParams = waveLayoutParams
            createConstrainAnimation(it, waveHeightLarge, waveHeightSmall, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_HEIGHT)
            createConstrainAnimation(it, waveMarginTopLarge, waveMarginTopSmall, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_MARGIN_TOP)
        }

        rulerView?.let {
            WaveItemView.updateTimeLinesAlpha(1f)
            createConstrainAnimation(it, 1f, 0f, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_ALPHA)
        }

        translationBtn?.let {
            it.alpha = 1f
            it.visibility = View.VISIBLE
            createAnimation(it, COUISpringAnimation.ALPHA, 1f, 0f, ANIMATION_RESPONSE, ANIMATION_BOUNDS)
        }
    }

    private fun createAnimationUp() {
        realTimeView?.let {
            it.translationY = 0f
            it.visibility = View.VISIBLE
            it.alpha = 1f
            createAnimation(it, COUISpringAnimation.ALPHA, 1f, 0f, ANIMATION_RESPONSE, ANIMATION_BOUNDS)
            createConstrainAnimation(it, waveMarginTopSmall, waveMarginTopLarge, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_MARGIN_TOP)
        }
        timerView?.let {
            it.translationY = 0f
            it.pivotX = (it.width / 2).toFloat()
            it.pivotY = 0f
            it.scaleY = 1f
            it.scaleX = 1f
            it.textSize = floor(timerViewSizeSmall)
            createConstrainAnimation(it, timerViewSizeSmall, timerViewSizeLarge, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_SIZE)
        }

        recorderTop?.let {
            it.translationY = 0f
            val recorderTopParams = it.layoutParams as? MarginLayoutParams
            recorderTopParams?.topMargin = floor(recorderTopSmall).toInt()
            it.layoutParams = recorderTopParams
            createConstrainAnimation(it, recorderTopSmall, recorderTopLarge, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_MARGIN_TOP)
        }

        waveGradientView?.let {
            it.translationY = 0f
            val waveLayoutParams = waveGradientView?.layoutParams as? MarginLayoutParams
            waveLayoutParams?.height = floor(waveHeightSmall).toInt()
            waveLayoutParams?.topMargin = floor(waveMarginTopSmall).toInt()
            waveGradientView?.layoutParams = waveLayoutParams
            createConstrainAnimation(it, waveHeightSmall, waveHeightLarge, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_HEIGHT)
            createConstrainAnimation(it, waveMarginTopSmall, waveMarginTopLarge, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_MARGIN_TOP)
        }

        rulerView?.let {
            WaveItemView.updateTimeLinesAlpha(0f)
            createConstrainAnimation(it, 0f, 1f, ANIMATION_RESPONSE, ANIMATION_BOUNDS, ANIMATION_TYPE_ALPHA)
        }

        translationBtn?.let {
            it.alpha = 0f
            it.visibility = View.GONE
            createAnimation(it, COUISpringAnimation.ALPHA, 0f, 1f, ANIMATION_RESPONSE, ANIMATION_BOUNDS)
        }
    }

    private fun createConstrainAnimation(
        view: View,
        startValue: Float,
        finalPosition: Float,
        response: Float,
        bounds: Float,
        animationType: Int
    ) {
        when (animationType) {
            ANIMATION_TYPE_HEIGHT -> {
                createAnimation(View(activity), COUISpringAnimation.SCALE_Y, startValue, finalPosition, response, bounds)
                    .addUpdateListener { couiDynamicAnimation, value, fl2 ->
                        val marginLayoutParams = view.layoutParams as? MarginLayoutParams
                        marginLayoutParams?.height = value.toInt()
                        view.layoutParams = marginLayoutParams
                    }
            }
            ANIMATION_TYPE_MARGIN_TOP -> {
                val translationY = finalPosition - startValue
                createAnimation(view, COUISpringAnimation.TRANSLATION_Y, 0f, translationY, response, bounds)
            }
            ANIMATION_TYPE_SIZE -> {
                if (view is TextView) {
                    val targetScale = finalPosition / startValue
                    createAnimation(view, COUISpringAnimation.SCALE_X, 1f, targetScale, response, bounds)
                    createAnimation(view, COUISpringAnimation.SCALE_Y, 1f, targetScale, response, bounds)
                }
            }
            ANIMATION_TYPE_ALPHA -> {
                // 透明度动画保持原有实现
                val dummyView = View(activity)
                val animation = createAnimation(dummyView, COUISpringAnimation.ALPHA, startValue, finalPosition, response, bounds)
                animation.addUpdateListener { _, value, _ ->
                    WaveItemView.updateTimeLinesAlpha(value)
                }
            }
            else -> DebugUtil.d(TAG, "animationType error, animation invalid")
        }
    }

    private fun createAnimation(
        view: View,
        type: COUIDynamicAnimation.ViewProperty,
        startValue: Float,
        finalPosition: Float,
        response: Float,
        bounds: Float
    ): COUISpringAnimation {
        val animation = COUISpringAnimation(view, type, finalPosition).apply {
            spring.setFinalPosition(finalPosition)
            spring.setResponse(response)
            spring.setBounce(bounds)
        }
        animation.setStartValue(startValue)
        animationList.add(AnimationItem(view, animation, startValue, finalPosition))
        animation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
            if (animationIsRunning()) {
                initViewAndAnimation()
            }
        }
        return animation
    }

    data class AnimationItem(val view: View, val animation: COUISpringAnimation, var startValue: Float, var finalPosition: Float)
}
/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: RecorderLayoutUtils1
 * Description: RecorderLayoutUtils1
 * Version: 1.0
 * Date: 2025/7/3
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/7/3      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.record;

import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;
import static android.view.View.GONE;

import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.utils.ViewUtils;
import com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout;

public class RecorderLayoutUtils {
    public static final String TAG = "RecorderLayoutUtils1";
    public static final int NUM_TWO = 2;
    public static final int DP_8 = 8;
    public static final int DP_20 = 20;
    public static final int DP_50 = 50;
    public static final int DP_55 = 55;
    public static final int DP_150 = 150;
    public static final int DP_450 = 450;
    public static final int DP_660 = 660;
    private static final int MIN_MARGIN = (int) ViewUtils.dp2px(DP_8, true);
    private static final int DP_50_TO_PX = (int) ViewUtils.dp2px(DP_50, true);
    private static final int DP_55_TO_PX = (int) ViewUtils.dp2px(DP_55, true);

    /**
     * 显示/隐藏 波形和转写页面
     * 适配新的动画系统和分屏/浮窗模式
     */
    public static void updateWaveViewAndOtherView(RecorderActivity activity, String from) {
        if (!activity.isInMultiWindowMode()) {
            return;
        }
        WaveViewGradientLayout waveGradientView = activity.getWaveGradientView();
        View realTimeView = activity.getRealTimeView();
        LinearLayout transcriptionBox = activity.getTranscriptionBox();
        if (waveGradientView == null || realTimeView == null || transcriptionBox == null) {
            return;
        }
        // 至少要经过一次onLayout，否则获取不到宽高，UI显示错误
        if (!waveGradientView.isLaidOut()) {
            DebugUtil.i(TAG, "updateWaveViewAndOtherView return by view not layout,from=" + from);
            return;
        }
        boolean[] showOrHideResult = getShowOrHideWaveViewAndMarkListView(activity);
        boolean canShowWaveView = showOrHideResult[0];
        boolean canShowWaveViewCenterDisplay = showOrHideResult[1];
        boolean canShowTranscriptionBtn = showOrHideResult[2];
        boolean canShowRealTimeView = showOrHideResult[3];
        DebugUtil.i(TAG, "from = " + from + ",canShowWaveView = " + canShowWaveView
                + " canShowWaveViewCenterDisplay = " + canShowWaveViewCenterDisplay);
        Boolean realTimeSwitchState = activity.getRealTimeSwitchState();
        if (canShowWaveView || canShowWaveViewCenterDisplay) {
            //有空间显示波形
            View rootView = activity.getRootView();
            int rootViewHeight = rootView.getHeight();
            int screenHeightDp = (int) ViewUtils.px2dp(rootViewHeight);
            if (realTimeSwitchState && screenHeightDp <= DP_450) {
                activity.setWaveViewVisibility(GONE);
            } else {
                activity.setWaveViewVisibility(VISIBLE);
            }
            if (!canShowWaveView) {
                // 如果空间不足，需要居中显示波形图
                handleWaveViewMargin(activity);
            }
        } else {
            //没有足够空间显示波形
            activity.setWaveViewVisibility(GONE);
            if (realTimeSwitchState && canShowRealTimeView) {
                realTimeView.setVisibility(VISIBLE);
            } else {
                realTimeView.setVisibility(GONE);
            }

            if (!canShowTranscriptionBtn) {
                transcriptionBox.setVisibility(GONE);
            }
            checkModifyViewLayout(activity);
        }
    }

    /**
     * 当布局小于450dp时，计算并调整布局
     * 检查并修改布局，保证转写按钮和时间显示控件之间的间距至少为8dp
     */
    private static void checkModifyViewLayout(RecorderActivity activity) {
        View transcriptionBox = activity.findViewById(R.id.transcription_tv);
        ViewGroup middleControl = activity.getMiddleControl();
        ViewGroup recordTop = activity.getRecordTop();
        TextView timerTextView = activity.getTimerTextView();
        if (transcriptionBox == null || middleControl == null || recordTop == null || timerTextView == null) {
            return;
        }
        // 隐藏标准模式标题 str5 删掉关于modeTitleText的代码即可
        // 1. 将时间显示控件的高度设置为50dp
        int minTimerHeight = activity.getResources().getDimensionPixelOffset(R.dimen.recorder_timer_view_min_height);
        int reduceHeight = minTimerHeight - DP_50_TO_PX;
        // 1. 计算middleControl与时间显示控件之间的距离
        int gap = middleControl.getTop() - recordTop.getBottom() + reduceHeight;
        DebugUtil.d(TAG, "checkTimeShow dp50ToPx: " + DP_50_TO_PX + " gap: " + gap + " minMargin: " + MIN_MARGIN);
        // 如果距离小于最小边距要求，需要进行调整
        if (gap > MIN_MARGIN) {
            DebugUtil.d(TAG, "checkTimeShow return by gap > minMargin");
            return;
        }
        // 2. 调整middleControl的底部外边距
        ViewGroup.MarginLayoutParams middleLayoutParams = (ViewGroup.MarginLayoutParams) middleControl.getLayoutParams();
        int currentBottomMargin = middleLayoutParams.bottomMargin;
        // 计算需要减少的边距来保证至少8dp间距
        int needReduceMargin = MIN_MARGIN - gap;
        int newBottomMargin = Math.max(MIN_MARGIN, currentBottomMargin - needReduceMargin);
        middleLayoutParams.bottomMargin = newBottomMargin;
        middleControl.setLayoutParams(middleLayoutParams);
        DebugUtil.d(TAG, "checkTimeShow set middleControl bottom margin: " + currentBottomMargin + " -> " + newBottomMargin);
        if (newBottomMargin != MIN_MARGIN) {
            DebugUtil.d(TAG, "checkTimeShow return by newBottomMargin more than minMargin");
            return;
        }
        // 3. 如果middleControl的底部边距已经达到最小值8dp，调整mRecordTop的topMargin
        recordTop.post(() -> {
            if (activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            // 重新计算调整后的间距
            int newGap = middleControl.getTop() - recordTop.getBottom();
            DebugUtil.d(TAG, "checkTimeShow newGqp: " + newGap);
            // 如果间距仍然不足8dp，调整mRecordTop的marginTop
            if (newGap < MIN_MARGIN) {
                int needAddMargin = MIN_MARGIN - newGap;
                int newMarginTop = newGap - needAddMargin;
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) recordTop.getLayoutParams();
                layoutParams.topMargin = newMarginTop;
                recordTop.setLayoutParams(layoutParams);
                DebugUtil.d(TAG, "mRecordTop marginTop: " + newMarginTop);
            }
        });
    }


    /**
     * 处理波形图的边距
     */
    private static void handleWaveViewMargin(RecorderActivity activity) {
        View transcriptionBox = activity.findViewById(R.id.transcription_tv);
        LinearLayout recordTop = activity.getRecordTop();
        WaveViewGradientLayout waveGradientView = activity.getWaveGradientView();
        TextView timerTextView = activity.getTimerTextView();
        if (transcriptionBox == null || recordTop == null || waveGradientView == null || timerTextView == null) {
            return;
        }
        ViewGroup.MarginLayoutParams waveGradientViewLayoutParams = (ViewGroup.MarginLayoutParams) waveGradientView.getLayoutParams();
        // 去掉上下边距，计算波形图的高度
        int waveBottom = waveGradientView.getBottom();
        int transcriptionTop = transcriptionBox.getTop();
        int distance = (int) (waveBottom - transcriptionTop + activity.getResources().getDimension(R.dimen.dp12));
        int height = transcriptionTop - recordTop.getBottom() - distance * 2;
        waveGradientViewLayoutParams.topMargin = distance + recordTop.getBottom();
        waveGradientViewLayoutParams.height = height;
        waveGradientView.setLayoutParams(waveGradientViewLayoutParams);
        DebugUtil.i(TAG, "handleWaveViewMargin topMargin = " + waveGradientViewLayoutParams.topMargin
                + " bottomMargin = " + waveGradientViewLayoutParams.bottomMargin + " height = " + waveGradientViewLayoutParams.height);
    }

    /**
     * @return 一个boolean[2]数组，
     * boolean[0]->能否显示波形 + 波形边距
     * boolean[1]->能否显示波形，居中显示
     * boolean[2]->能否转写按钮
     */
    public static boolean[] getShowOrHideWaveViewAndMarkListView(RecorderActivity activity) {
        ViewGroup middleControl = activity.getMiddleControl();
        ViewGroup recordTop = activity.getRecordTop();
        LinearLayout transcriptionBox = activity.getTranscriptionBox();
        View waveGradientView = activity.getWaveGradientView();
        View rootView = activity.getRootView();
        if (middleControl == null || recordTop == null || transcriptionBox == null
                || rootView == null) {
            return new boolean[]{true, false, false};
        }
        //能否显示波形00
        boolean canShowWaveView = false;
        boolean canShowWaveViewCenterDisplay = false;
        boolean canShowTranscriptionBtn = false;
        boolean canShowRealTimeView = false;
        int contentHeight = middleControl.getTop() - recordTop.getBottom();
        int waveHeight = 0;
        RecordViewAnimationControl viewAnimateControl = activity.getViewAnimateControl();
        if (viewAnimateControl != null) {
            waveHeight = viewAnimateControl.getWaveHeight();
        }
        int transcriptionHeight = transcriptionBox.getHeight()
                + activity.getResources().getDimensionPixelOffset(R.dimen.transcription_box_margin_bottom);
        int rootViewHeight = rootView.getHeight();
        int screenHeightDp = (int) ViewUtils.px2dp(rootViewHeight);
        int waveBottom = waveGradientView.getBottom();
        int transcriptionTop = transcriptionBox.getTop();
        int distance = (int) (waveBottom - transcriptionTop + activity.getResources().getDimension(R.dimen.dp12));
        int waveViewHeight = transcriptionTop - recordTop.getBottom() - distance * 2;
        int waveViewMarginTop = activity.getResources().getDimensionPixelOffset(R.dimen.wave_margin_top);
        DebugUtil.i(TAG, "contentHeight = " + contentHeight + "，waveViewHeight = " + waveHeight
                + " waveViewMarginTop = " + waveViewMarginTop + " screenHeightDp = " + screenHeightDp);
        int instance = waveGradientView.getTop() - recordTop.getBottom() + transcriptionTop - waveBottom;
        if (contentHeight - waveViewHeight - instance - transcriptionHeight >= 0 || screenHeightDp >= DP_660) {
            DebugUtil.i(TAG, "**********  contentHeight - waveViewHeight - instance - transcriptionHeight " + (transcriptionTop - recordTop.getBottom() + transcriptionTop - waveBottom));
            // 当空间足够显示波形图加上边距时
            canShowWaveView = true;
            canShowWaveViewCenterDisplay = true;
            canShowTranscriptionBtn = true;
        } else if (contentHeight - waveViewHeight - transcriptionHeight >= 0 || screenHeightDp >= DP_450) {
            DebugUtil.i(TAG, "**********  contentHeight - waveViewHeight - instance - transcriptionHeight " + (contentHeight - waveViewHeight - transcriptionHeight));
            DebugUtil.i(TAG, "22222222222");
            canShowWaveViewCenterDisplay = true;
            canShowTranscriptionBtn = true;
        } else if (contentHeight - transcriptionHeight >= 0) {
            DebugUtil.i(TAG, "33333333333");
            // 当空间不足显示波形图加上边距时，转写按钮居中显示
            canShowTranscriptionBtn = true;
        }
        if (screenHeightDp > DP_150) {
            canShowRealTimeView = true;
        }
        return new boolean[]{canShowWaveView, canShowWaveViewCenterDisplay, canShowTranscriptionBtn, canShowRealTimeView};
    }
}

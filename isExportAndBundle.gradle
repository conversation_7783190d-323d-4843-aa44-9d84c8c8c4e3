def isOMSReleaseBuildType = {
    def split = getGradle().getStartParameter().getTaskRequests().toString().split("=");
    if (split.length > 2) {
        String s = split[1].toString().split("]")[0]
        String name = s.toLowerCase()
        if (name.contains("domestic") || name.contains("rall")) {
            return true
        }
    } else {
        return false
    }
}

def isDomesticOmsTask = {
    def taskNames = getGradle().getStartParameter().getTaskNames().toString().toLowerCase()
    //oms插件本地debug版本
    //def enable = taskNames.contains("domestic") && taskNames.contains("debug")
//    def enable = taskNames.contains("domestic") && taskNames.contains("release")
    def enable = taskNames.contains("domestic")
    println("-----> isDomesticOmsTask: $enable , taskNames: $taskNames, ")
    return enable
}

ext {
    fun = isOMSReleaseBuildType
    domesticOms = isDomesticOmsTask
}
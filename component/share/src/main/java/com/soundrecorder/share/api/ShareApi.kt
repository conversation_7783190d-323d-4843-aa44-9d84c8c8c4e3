/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareApi
 * * Description: Share<PERSON>pi
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.share.api

import android.app.Activity
import android.view.View
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.share.ShareManager
import com.soundrecorder.share.normal.link.utils.ShareLinkUtils
import kotlinx.coroutines.CoroutineScope

object ShareApi : ShareAction {

    /**
     * 分享入口
     * @param shareTextContent 文本数据
     * @param coroutineScope 分享协程作用域
     * @param shareListener 分享状态监听
     * @param type 分享类型，不同的分享可能携带有自己特有的数据
     */
    override fun <T> share(
        activity: Activity?,
        shareTextContent: T,
        type: ShareType,
        coroutineScope: CoroutineScope?,
        shareListener: IShareListener?,
        jumpToNote: Boolean?
    ) {
        if (shareTextContent is ShareTextContent) {
            ShareManager.share(activity, shareTextContent, coroutineScope, shareListener, type, jumpToNote)
        }
    }

    override fun registerShareListener(listener: IShareListener) {
        ShareManager.registerShareListener(listener)
    }

    override fun unregisterShareListener(listener: IShareListener) {
        ShareManager.unregisterShareListener(listener)
    }

    override fun showShareLinkPanel(activity: Activity, link: String, anchor: View?) {
        ShareLinkUtils.showShareLinkPanel(activity, link, anchor)
    }

    override fun canUploadMoreAudioFiles(): Boolean {
        return true
    }
}
/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:FileUploader.kt
 * * Description: ProgressRequestBody
 * * Version: 1.0
 * * Date : 2025/7/10
 * * Author: wangxiong
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *   tianjian    2025/7/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager

import android.annotation.SuppressLint
import android.app.Activity
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.widget.RemoteViews
import androidx.appcompat.app.AlertDialog
import androidx.core.app.NotificationCompat
import androidx.core.app.ServiceCompat.stopForeground
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.share.R
import java.util.UUID

class LinkSharedNotificationHelper {

    companion object {
        private const val FORGE_BACKGROUND_CHANNEL_ID = "link_shared_upload_channel"
        private const val NOTIFY_CHANEL_ID = "link_shared_upload_notification_channel"
        private const val FORGE_BACKGROUND_NOTIFICATION_ID = 1011
        private const val NOTIFICATION_ID = 1012
        const val ACTION_CANCEL = "com.soundrecorder.share.action.CANCEL"
        const val ACTION_RETRY = "com.soundrecorder.share.action.RETRY"
        private const val TAG = "LinkSharedNotificationHelper"
        private const val PROGRESS_MAX = 100
        private const val PROGRESS_MIN = 0
        private const val NOTIFICATION_INTENT_CANCEL_CODE = 10001
        private const val NOTIFICATION_INTENT_RETRY_CODE = 10002
        private const val DIALOG_INTENT_CANCEL_CODE = 10003
        private const val DIALOG_INTENT_RETRY_CODE = 10004
        private const val NOTIFICATION_UPDATE_DELAY = 500L
        private const val PROGRESS_DIFF = 5
        private const val IS_NEED_SHOW_SHARE_PANEL = "IS_NEED_SHOW_SHARE_PANEL"
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    private val forgeBackgroundNotificationGroupId = UUID.randomUUID().toString()
    private val notificationGroupId = UUID.randomUUID().toString() + System.currentTimeMillis()
    private var notificationManager: NotificationManager
    private var mProgressAlertDialog: AlertDialog? = null
    private var mSingleAlertDialog: AlertDialog? = null
    private var mNetWorkAlertDialog: AlertDialog? = null
    private var notificationUpdateRunner: Runnable? = null

    private var mCacheErrorCode = 0

    private var mLastProgress = 0

    constructor(context: Service) {
        notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        createForgeBackgroundChannel()
        createNotificationChannel()
    }


    private fun createForgeBackgroundChannel() {
        val channel = NotificationChannel(
            FORGE_BACKGROUND_CHANNEL_ID, "audio file uploading", NotificationManager.IMPORTANCE_HIGH
        ).apply {
            description = "to show upload progress"
        }
        notificationManager.createNotificationChannel(channel)
    }


    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            NOTIFY_CHANEL_ID, "audio file uploaded result", NotificationManager.IMPORTANCE_HIGH
        ).apply {
            description = "to show upload result"
        }
        notificationManager.createNotificationChannel(channel)
    }

    @SuppressLint("ForegroundServiceType")
    @Suppress("MagicNumber")
    private fun createOrUpdateNotification(
        context: Service,
        progress: Int,
        errorCode: Int,
        targetActivity: Activity,
        isCreate: Boolean,
        netWorkErrorMessage: String? = null,
    ) {
        val title = netWorkErrorMessage ?: LinkShareMessageHelper.getMessageTitle(errorCode)
        val expandViews = setRemoteViewsInfo(
            context,
            RemoteViewsConfig(progress = progress, context = context, netWorkErrorMessage = netWorkErrorMessage, title = title, errorCode = errorCode)
        )
        val isNeedForeground = isCreate || (errorCode == LinkShareMessageHelper.CODE_DOING_SUCCESS) || LinkShareMessageHelper.isNeedRetry(errorCode)
        DebugUtil.d(TAG, "createOrUpdateNotification isNeedForeground: $isNeedForeground title: $title progress = $progress  errorCode: $errorCode")
        val intent = Intent(context, targetActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra(IS_NEED_SHOW_SHARE_PANEL, true)
        }
        val flags = PendingIntent.FLAG_UPDATE_CURRENT or (PendingIntent.FLAG_IMMUTABLE)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, flags
        )

        val channelId = if (isNeedForeground) FORGE_BACKGROUND_CHANNEL_ID else NOTIFY_CHANEL_ID
        val groupId = if (isNeedForeground) forgeBackgroundNotificationGroupId else notificationGroupId
        val builder = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
            .setContentIntent(pendingIntent)
            .setOnlyAlertOnce(false)
            .setContentTitle(title)
            .setGroup(groupId)
            .setGroupAlertBehavior(NotificationCompat.GROUP_ALERT_ALL)
            .setPriority(NotificationCompat.PRIORITY_HIGH)

        if (isNeedForeground) {
            builder.setAutoCancel(false)
                .setOngoing(true)
                .setCustomBigContentView(expandViews)
                .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
                .setStyle(NotificationCompat.DecoratedCustomViewStyle())
        } else {
            builder.setAutoCancel(true)
                .setOngoing(false)
        }

        val notification = builder.build()

        if (isCreate) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                context.startForeground(FORGE_BACKGROUND_NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC)
            } else {
                context.startForeground(FORGE_BACKGROUND_NOTIFICATION_ID, notification)
            }
        } else {
            val notifyId = if (isNeedForeground) FORGE_BACKGROUND_NOTIFICATION_ID else NOTIFICATION_ID
            notificationManager.notify(notifyId, notification)
        }
    }

    private fun setRemoteViewsInfo(
        context: Context,
        config: RemoteViewsConfig
    ): RemoteViews? {
        var expandViews: RemoteViews? = null
        when {
            config.errorCode == LinkShareMessageHelper.CODE_DOING_SUCCESS -> {
                expandViews = RemoteViews(context.packageName, R.layout.notification_upload_large)
                expandViews.setOnClickPendingIntent(R.id.cancel, createCancelPendingIntent(config.context, NOTIFICATION_INTENT_CANCEL_CODE))
                expandViews.setProgressBar(R.id.share_notification_progress, PROGRESS_MAX, config.progress, false)
            }

            config.errorCode != LinkShareMessageHelper.CODE_DOING_SUCCESS && LinkShareMessageHelper.isNeedRetry(config.errorCode) -> {
                expandViews = RemoteViews(context.packageName, R.layout.notification_upload_large_error)
                expandViews.setOnClickPendingIntent(R.id.cancel, createCancelPendingIntent(config.context, NOTIFICATION_INTENT_CANCEL_CODE))
                expandViews.setOnClickPendingIntent(R.id.retry, createRetryPendingIntent(config.context, NOTIFICATION_INTENT_RETRY_CODE))
            }
        }

        expandViews?.setTextViewText(R.id.notification_title, config.title)
        return expandViews
    }

    private fun postDelayCreateOrUpdateNotification(
        context: Service,
        progress: Int,
        errorCode: Int,
        targetActivity: Activity,
        isCreate: Boolean,
        netWorkErrorMessage: String? = null
    ) {
        notificationUpdateRunner?.let { mainHandler.removeCallbacks(it) }
        notificationUpdateRunner = Runnable {
            createOrUpdateNotification(context, progress, errorCode, targetActivity, isCreate, netWorkErrorMessage)
            notificationUpdateRunner = null
        }
        notificationUpdateRunner?.let {
            mainHandler.postDelayed(it, NOTIFICATION_UPDATE_DELAY)
        }
    }

    private fun createCancelPendingIntent(context: Context, requestCode: Int = 0): PendingIntent? {
        val cancelIntent = Intent(context, LinkShareReceiver::class.java).apply {
            action = ACTION_CANCEL
        }
        return PendingIntent.getBroadcast(
            context,
            requestCode,
            cancelIntent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    private fun createRetryPendingIntent(context: Context, requestCode: Int = 0): PendingIntent? {
        val cancelIntent = Intent(context, LinkShareReceiver::class.java).apply {
            action = ACTION_RETRY
        }
        return PendingIntent.getBroadcast(
            context,
            requestCode,
            cancelIntent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    private fun createProgressDialog(context: Context) {
        mProgressAlertDialog = COUIAlertDialogBuilder(
            context, com.support.dialog.R.style.COUIAlertDialog_Progress_Cancelable
        ).let {
            it.setTitle(context.getString(com.soundrecorder.base.R.string.link_share_generating))
            it.setPositiveButton(
                context.getString(com.soundrecorder.base.R.string.cancel)
            ) { _, _ ->
                DebugUtil.d(TAG, "mProgressAlertDialog onCancel")
                createCancelPendingIntent(context, DIALOG_INTENT_CANCEL_CODE)?.send()
            }
            it.setCancelable(false)
            it.show()
        }
    }

    private fun createSingleDialog(context: Context, errorCode: Int) {
        mSingleAlertDialog = COUIAlertDialogBuilder(
            context,
            com.support.dialog.R.style.COUIAlertDialog_Bottom
        ).apply {
            setTitle(LinkShareMessageHelper.getMessageTitle(errorCode))
            if (LinkShareMessageHelper.isNeedRetry(errorCode)) {
                setNegativeButton(context.getString(com.soundrecorder.base.R.string.cancel)) { dialog, _ ->
                    DebugUtil.d(TAG, "mSingleAlertDialog# send cancel PendingIntent")
                    createCancelPendingIntent(
                        context,
                        DIALOG_INTENT_CANCEL_CODE
                    )?.send()
                }

                setPositiveButton(context.getString(com.soundrecorder.base.R.string.retry)) { dialog, _ ->
                    DebugUtil.d(TAG, "mSingleAlertDialog# send retry PendingIntent")
                    createRetryPendingIntent(
                        context,
                        DIALOG_INTENT_RETRY_CODE
                    )?.send()
                }
            } else {
                setNegativeButton(context.getString(com.soundrecorder.base.R.string.button_ok)) { dialog, _ -> mSingleAlertDialog = null }
            }
            setCancelable(false)
            setOnCancelListener {
                mSingleAlertDialog = null
            }
        }.show()
    }

    private fun createNetWorkDialog(context: Context, errorCode: Int, messageContent: String) {
        mNetWorkAlertDialog = COUIAlertDialogBuilder(
            context,
            com.support.dialog.R.style.COUIAlertDialog_Bottom
        ).apply {
            setTitle(LinkShareMessageHelper.getMessageTitle(errorCode))
            setMessage(messageContent)
            setNegativeButton(context.getString(com.soundrecorder.base.R.string.cancel)) { dialog, _ ->
                DebugUtil.d(TAG, "createNetWorkDialog# send cancel PendingIntent")
                createCancelPendingIntent(
                    context,
                    DIALOG_INTENT_CANCEL_CODE
                )?.send()
            }
            setPositiveButton(context.getString(com.soundrecorder.base.R.string.share)) { dialog, _ ->
                DebugUtil.d(TAG, "createNetWorkDialog# send retry PendingIntent")
                createRetryPendingIntent(
                    context,
                    DIALOG_INTENT_RETRY_CODE
                )?.send()
            }
            setCancelable(false)
            setOnCancelListener { mNetWorkAlertDialog = null }
        }.show()
    }


    fun doErrorMessage(context: Service, targetActivity: Activity, errorCode: Int) {
        mCacheErrorCode = errorCode
        mainHandler.post {
            resetAllDialog()
            postDelayCreateOrUpdateNotification(context, progress = 0, errorCode = errorCode, targetActivity, false)
            createSingleDialog(targetActivity, errorCode)
        }
    }

    fun resetAllDialog() {
        listOf(mProgressAlertDialog, mNetWorkAlertDialog, mSingleAlertDialog).forEach {
            it?.let { dialog ->
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
            }
        }
        mProgressAlertDialog = null
        mNetWorkAlertDialog = null
        mSingleAlertDialog = null
    }

    fun doUpdateProgress(context: Service, targetActivity: Activity, progress: Int) {
        if (progress - mLastProgress < PROGRESS_DIFF) {
            return
        }
        mLastProgress = progress
        mainHandler.post {
            mCacheErrorCode = LinkShareMessageHelper.CODE_DOING_SUCCESS
            mProgressAlertDialog?.findViewById<COUIHorizontalProgressBar>(
                com.support.dialog.R.id.progress
            )?.progress = progress
            postDelayCreateOrUpdateNotification(context, progress, LinkShareMessageHelper.CODE_DOING_SUCCESS, targetActivity, false)
        }
    }

    fun initAndStart(context: Service, targetActivity: Activity) {
        DebugUtil.d(TAG, "initAndStart ............")
        mainHandler.post {
            mCacheErrorCode = 0
            resetAllDialog()
            postDelayCreateOrUpdateNotification(context, PROGRESS_MIN, LinkShareMessageHelper.CODE_DOING_SUCCESS, targetActivity, true)
            createProgressDialog(targetActivity)
        }
    }

    fun doCompleted(context: Service, targetActivity: Activity) {
        DebugUtil.d(TAG, "#doCompleted")
        mainHandler.post {
            resetAllDialog()
            createOrUpdateNotification(context, PROGRESS_MIN, LinkShareMessageHelper.CODE_SUCCESS_COMPLETED, targetActivity, false)
        }
    }

    fun doNetWorkError(context: Service, targetActivity: Activity, errorCode: Int, messageContent: String) {
        mCacheErrorCode = errorCode
        mainHandler.post {
            resetAllDialog()
            postDelayCreateOrUpdateNotification(context, PROGRESS_MIN, errorCode, targetActivity, false, messageContent)
            createNetWorkDialog(targetActivity, errorCode, messageContent)
        }
    }

    fun onDestroy() {
        mainHandler.post {
            DebugUtil.d(TAG, "cancel notification")
            notificationUpdateRunner?.let { mainHandler.removeCallbacks(it) }
            notificationManager.cancel(FORGE_BACKGROUND_NOTIFICATION_ID)

            //中断不可恢复的错误提示要保留
            if (LinkShareMessageHelper.isNeedRetry(mCacheErrorCode)) {
                resetAllDialog()
            } else {
                mProgressAlertDialog?.cancel()
                mProgressAlertDialog = null
            }
        }
    }
}

private data class RemoteViewsConfig(
    val context: Service,
    val progress: Int,
    val title: String,
    val netWorkErrorMessage: String?,
    val errorCode: Int
)
/***********************************************************
 * * Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 * * COLOROS_EDIT
 * * File:  LinkShareManager.kt
 * * Description: LinkShareManager
 * * Version: 1.0
 * * Date : 2025/3/7
 * * Author: wangxiong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * <EMAIL>    2025/3/7    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager

import android.Manifest
import android.app.Activity
import android.app.Service
import android.content.Intent
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET
import android.net.NetworkCapabilities.TRANSPORT_CELLULAR
import android.net.NetworkCapabilities.TRANSPORT_WIFI
import android.net.NetworkRequest
import android.os.Binder
import android.os.IBinder
import androidx.annotation.RequiresPermission
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.share.ShareManager
import com.soundrecorder.share.normal.link.bean.ChunkBean
import com.soundrecorder.share.normal.link.bean.PartUrl
import com.soundrecorder.share.normal.link.bean.PreSignatureResponseContent
import com.soundrecorder.share.normal.link.bean.ShareRegisterResponseContent
import com.soundrecorder.share.normal.link.manager.listener.ILinkShareListener
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import org.koin.core.component.getScopeName
import java.io.File
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.min

class LinkShareService : Service() {
    companion object {
        const val TAG = "LinkShareService"
        const val CHUNK_SIZE: Long = 1024 * 1024 * 2
        const val MAX_LARGE_FILE_SIZE = 200 * 1024 * 1024
        const val FILE_SIZE_MB = 1024 * 1024
        const val MAX_UPLOAD_COUNT = 5
        const val MAX_GET_BATCH_COUNT = 5
        const val PROGRESS_UNIT = 100
        const val STATUS_REGISTER = 1
        const val STATUS_PRE_SING = 2
        const val STATUS_IN_UPLOAD = 3
        const val STATUS_IN_PAUSE = 4
        const val STATUS_DESTROYED = -1
        const val FILE_SIZE_TO_CHUNK = 5 * 1024 * 1024
        const val PRE_SING_CHUNK_NUMBER = 5
        const val WAITING_FOR_FETCH_BATCH = 100L
        private var Instance: LinkShareService? = null

        fun getInstance(): LinkShareService? {
            return Instance
        }
    }

    private var mLinkShareListener: ILinkShareListener? = null
    private var mShareTextContent: ShareTextContent? = null
    private var mShareType: ShareTypeLink? = null
    private var mServiceScope: CoroutineScope? = null

    private val mCacheChunkList = mutableListOf<ChunkBean>()
        get() = synchronized(chunkCacheLock) { field }
    private val mCompleteChunkList = mutableListOf<ChunkBean>()
        get() = synchronized(chunkCacheLock) { field }
    private val chunkCacheLock = Any()
    private var mTotalChunk = 0
    private val linkShareListener: ILinkShareListener
        get() = mLinkShareListener ?: throw NullPointerException("mLinkShareListener is null")

    private val shareTextContent: ShareTextContent
        get() = mShareTextContent ?: throw NullPointerException("mShareTextContent is null")
    private val shareType: ShareTypeLink
        get() = mShareType ?: throw NullPointerException("mShareType is null")
    private val serviceScope: CoroutineScope
        get() = mServiceScope ?: throw NullPointerException("mServiceScope is null")

    private var mRegisterResponse: ShareRegisterResponseContent? = null

    private val connectivityManager by lazy {
        getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
    }
    private lateinit var networkCallback: ConnectivityManager.NetworkCallback

    private var mStatus = 0
    private var mFileId: String? = null

    private var targetActivity: WeakReference<Activity>? = null

    var mNotificationHelper: LinkSharedNotificationHelper? = null

    private var mFileSize = 0L

    private var mUploadFileSize = 0L

    private var isMobileNetwork = false

    private var isDoneError = false

    private var isCompleteFetchChunks = false

    private var startUploadTime = 0L


    private fun createNetworkCallback() = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            // 网络可用
        }

        override fun onLost(network: Network) {
            doError(LinkShareMessageHelper.CODE_DIALOG_NO_NETWORK)
        }

        override fun onCapabilitiesChanged(network: Network, capabilities: NetworkCapabilities) {
            isMobileNetwork = !capabilities.hasTransport(TRANSPORT_WIFI)
        }
    }

    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    fun registerNetWorkCallback() {
        networkCallback = createNetworkCallback()
        val request = NetworkRequest.Builder()
            .addCapability(NET_CAPABILITY_INTERNET)
            .addTransportType(TRANSPORT_WIFI)
            .addTransportType(TRANSPORT_CELLULAR)
            .build()
        connectivityManager.registerNetworkCallback(request, networkCallback)
    }

    override fun onBind(p0: Intent?): IBinder? {
        DebugUtil.d(TAG, "onBind")
        return LinkShareBinder()
    }


    fun setShareContextAndStartUpload(shareTextContent: ShareTextContent, type: ShareTypeLink, listener: ILinkShareListener, activity: Activity) {
        mShareTextContent = shareTextContent
        mShareType = type
        mLinkShareListener = listener
        mServiceScope = CoroutineScope(Dispatchers.IO + SupervisorJob() + CoroutineExceptionHandler { coroutineContext, throwable ->
            DebugUtil.e(TAG, coroutineContext.getScopeName().toString(), throwable)
            clearChunkCache()
            doError(LinkShareMessageHelper.CODE_DIALOG_GENERATE_FAIL)
        })
        targetActivity = WeakReference(activity)
        mFileSize = shareType.record?.data?.let { File(it).length() } ?: 0
        mNotificationHelper = LinkSharedNotificationHelper(this)
        targetActivity?.get()?.let {
            mNotificationHelper?.initAndStart(this, it)
        }
        startUploader()
    }

    inner class LinkShareBinder : Binder() {
        fun getService(): LinkShareService {
            return this@LinkShareService
        }
    }

    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    override fun onCreate() {
        super.onCreate()
        DebugUtil.d(TAG, "onCreate")
        registerNetWorkCallback()
        Instance = this
    }


    override fun onDestroy() {
        super.onDestroy()
        synchronized(this) {
            mStatus = STATUS_DESTROYED
            mNotificationHelper?.onDestroy()
            DebugUtil.d("TDAD", "desadasdadasdasdasdasdasdasd")
            stopForeground(Service.STOP_FOREGROUND_REMOVE)
            AudioFileChunkUploader.cancelAllUploadFile()
        }
        DebugUtil.d(TAG, "onDestroy")
        mServiceScope?.cancel()
        mServiceScope = null
        mShareType = null
        mShareTextContent = null
        mLinkShareListener = null
        mCacheChunkList.clear()
        mCompleteChunkList.clear()
        mTotalChunk = 0
        connectivityManager.unregisterNetworkCallback(networkCallback)
        Instance = null
    }

    fun retryLinkShare() {
        DebugUtil.d(TAG, "retryLinkShare# status = $mStatus")
        isDoneError = false
        if (mStatus != STATUS_IN_PAUSE) return
        changeStatus(STATUS_IN_UPLOAD)
        val completeSize = mCompleteChunkList.size * CHUNK_SIZE
        mUploadFileSize = if (completeSize < mFileSize) completeSize else mFileSize
        targetActivity?.get()?.let { activity ->
            mNotificationHelper?.initAndStart(this, activity)
        }
        mFileId?.let { fileId ->
            doGetBatchUrl(fileId)
            doUploadChunk()
        } ?: startUploader()
    }


    private fun startUploader() {
        changeStatus(STATUS_REGISTER)
        val filePath = shareType.record?.data
        val startTime = System.currentTimeMillis()
        LinkShareManager.instance.linkShare(shareTextContent, shareType) { errorCode, response ->
            mRegisterResponse = response
            val duration = System.currentTimeMillis() - startTime
            DebugUtil.d(TAG, "startUploader# #register duration = $duration")
            when {
                errorCode != 0 -> doError(errorCode)
                response == null -> doError(LinkShareMessageHelper.CODE_DIALOG_GENERATE_FAIL)
                shareType.record?.getmHasRecordFile() != -1L && filePath == null -> doError(LinkShareMessageHelper.CODE_DIALOG_GENERATE_FAIL)
                shareType.record?.getmHasRecordFile() != -1L && filePath != null -> {
                    if (doMobileNetworkError()) return@linkShare
                    if (mFileSize > MAX_LARGE_FILE_SIZE) {
                        doError(LinkShareMessageHelper.CODE_TOAST_FILE_LARGE)
                    } else {
                        doUploadPreSinUrl(response.shareId)
                    }
                }

                else -> doComplete()
            }
        }
    }


    private fun changeStatus(status: Int) {
        synchronized(this) {
            mStatus = status
        }
    }

    private fun doUploadPreSinUrl(shareId: String) {
        changeStatus(STATUS_PRE_SING)
        mTotalChunk = AudioFileChunkUploader.calculateTotalChunks(mFileSize, CHUNK_SIZE)
        val isNeedChunk = mFileSize >= FILE_SIZE_TO_CHUNK
        val preChunkNumber = if (isNeedChunk) min(mTotalChunk, PRE_SING_CHUNK_NUMBER) else 0
        val startTime = System.currentTimeMillis()
        AudioFileChunkUploader.getPreSignUrl(
            shareId,
            shareType.record!!,
            shareTextContent.mediaRecordId,
            preChunkNumber
        ) { errorCode, response ->
            val duration =  System.currentTimeMillis() - startTime
            DebugUtil.d(TAG, "doUploadPreSinUrl# #getPreSignUrl# duration = $duration")
            when {
                errorCode == LinkShareMessageHelper.CODE_DOING_SUCCESS && response?.exist == true -> doComplete()
                errorCode == LinkShareMessageHelper.CODE_DOING_SUCCESS && response?.exist == false -> {
                    startUploadTime = System.currentTimeMillis()
                    serviceScope.launch {
                        changeStatus(STATUS_IN_UPLOAD)
                        mFileId = response.fileId
                        when {
                            isNeedChunk -> {
                                addToCacheChunkList(response.fileId, response.partUrls)
                                doGetBatchUrl(response.fileId)
                                doUploadChunk()
                            }

                            else -> doUploadRecordFile(response)
                        }
                    }
                }

                else -> doError(errorCode)
            }
        }
    }

    private fun doUploadRecordFile(response: PreSignatureResponseContent) {
        DebugUtil.d(TAG, "doUploadRecordFile#")
        serviceScope.launch {
            val filePath = shareType.record?.data
            val fileId = mFileId
            if (filePath != null && fileId != null) {
                val success = async {
                    AudioFileChunkUploader.uploadRecordFile(filePath, response.signUrl) { uploaded -> doUpdateProgress(uploaded) }
                }.await()
                val fileSize  = "%.2f".format(mFileSize / FILE_SIZE_MB.toFloat())
                val duration = System.currentTimeMillis() - startUploadTime
                DebugUtil.d(TAG, "doUploadRecordFile# file-size=$fileSize upload-duration = $duration")
                if (success) {
                    AudioFileChunkUploader.uploadCompleteVerify(fileId) { errorCode ->
                        when (errorCode) {
                            LinkShareMessageHelper.CODE_DOING_SUCCESS -> doComplete()
                            else -> {
                                DebugUtil.d(TAG, "doUploadRecordFile# errorCode = $errorCode")
                                doError(errorCode)
                            }
                        }
                    }
                } else {
                    DebugUtil.d(TAG, "doUploadRecordFile# errorCode 107")
                    doError(LinkShareMessageHelper.CODE_DIALOG_GENERATE_FAIL)
                }
            }
        }
    }

    private fun doGetBatchUrl(fileId: String) {
        DebugUtil.d(TAG, "#doGetBatchUrl  totalChunk = $mTotalChunk cacheSize = ${mCacheChunkList.size} completeSize = ${mCompleteChunkList.size}")
        serviceScope.launch(CoroutineName("#doGetBatchUrl")) {
            val allChunks = (mCacheChunkList + mCompleteChunkList).map { it.chunkIndex }.toSet()
            val allRequiredChunks = (1..mTotalChunk).toSet()
            val missingChunks = (allRequiredChunks - allChunks).sorted()
            var index = 0
            isCompleteFetchChunks = false
            while (index < missingChunks.size && mStatus == STATUS_IN_UPLOAD) {
                val batch = missingChunks.subList(index, min(index + MAX_GET_BATCH_COUNT, missingChunks.size))
                async {
                    AudioFileChunkUploader.getBatchParts(batch.toIntArray(), fileId) { errorCode, data ->
                        when {
                            errorCode == LinkShareMessageHelper.CODE_DOING_SUCCESS && data != null -> {
                                launch {
                                    addToCacheChunkList(fileId, data.partUrls)
                                }
                            }

                            else -> {
                                isCompleteFetchChunks = true
                                doError(errorCode)
                            }
                        }
                    }
                }.await()
                index += MAX_GET_BATCH_COUNT
            }
            isCompleteFetchChunks = true
            DebugUtil.d(
                TAG,
                "#doGetBatchUrl allChunks=${(allChunks + missingChunks).sorted().joinToString(",")}"
            )
        }
    }

    private fun doUploadChunk() {
        DebugUtil.d(TAG, "doUploadChunk# Start uploading chunks, current status: $mStatus")
        serviceScope.launch(CoroutineName("#doUploadChunk")) {
            val chunkChannel = Channel<ChunkBean>(MAX_UPLOAD_COUNT)
            val hasFailed = AtomicBoolean(false)
            val producerJob = startChunkProducer(chunkChannel)
            startChunkConsumers(chunkChannel, hasFailed)
            waitForCompletion(producerJob, hasFailed)
        }
    }

    private fun CoroutineScope.startChunkProducer(chunkChannel: Channel<ChunkBean>): Job {
        return launch {
            var shouldContinue = true
            while (shouldContinue && mStatus == STATUS_IN_UPLOAD && !doMobileNetworkError()) {
                val chunk = synchronized(chunkCacheLock) {
                    mCacheChunkList.removeFirstOrNull()
                }
                when {
                    chunk != null -> {
                        DebugUtil.d(TAG, "doUploadChunk# Sending chunk ${chunk.chunkIndex} to upload queue")
                        chunkChannel.send(chunk)
                    }

                    !isCompleteFetchChunks -> delay(WAITING_FOR_FETCH_BATCH)
                    else -> shouldContinue = false
                }
            }
            chunkChannel.close()
            DebugUtil.d(TAG, "doUploadChunk# Producer finished, channel closed")
        }
    }

    private fun CoroutineScope.startChunkConsumers(
        chunkChannel: Channel<ChunkBean>,
        hasFailed: AtomicBoolean
    ) {
        repeat(MAX_UPLOAD_COUNT) {
            launch {
                for (chunk in chunkChannel) {
                    if (hasFailed.get() || doMobileNetworkError()) return@launch
                    processChunk(chunk, hasFailed)
                }
            }
        }
    }

    private suspend fun processChunk(chunk: ChunkBean, hasFailed: AtomicBoolean) {
        DebugUtil.d(TAG, "doUploadChunk# Processing chunk ${chunk.chunkIndex}")
        val result = AudioFileChunkUploader.uploadChunk(chunk, shareType.record!!.data)
        when {
            result != null -> handleUploadSuccess(result)
            else -> handleUploadFailure(chunk, hasFailed)
        }
    }

    private fun handleUploadSuccess(result: ChunkBean) {
        DebugUtil.d(TAG, "doUploadChunk# Successfully uploaded chunk ${result.chunkIndex}")
        synchronized(chunkCacheLock) {
            mCompleteChunkList.add(result)
        }
        doUpdateProgress()
    }

    private fun handleUploadFailure(chunk: ChunkBean, hasFailed: AtomicBoolean) {
        DebugUtil.d(TAG, "doUploadChunk# Failed to upload chunk ${chunk.chunkIndex}, will retry")
        synchronized(chunkCacheLock) {
            mCacheChunkList.add(chunk)
        }
        hasFailed.set(true)
        doError(LinkShareMessageHelper.CODE_DIALOG_GENERATE_FAIL)
    }

    private suspend fun CoroutineScope.waitForCompletion(producerJob: Job, hasFailed: AtomicBoolean) {
        coroutineContext[Job]?.children?.forEach { it.join() }
        producerJob.cancel()

        DebugUtil.d(
            TAG, "doUploadChunk# Final status - " +
                    "Failed: ${hasFailed.get()}, " +
                    "Pending chunks: ${mCacheChunkList.size}, " +
                    "Completed chunks: ${mCompleteChunkList.size}"
        )

        if (!hasFailed.get() && mCacheChunkList.isEmpty()) {
            DebugUtil.d(TAG, "doUploadChunk# All chunks uploaded successfully, verifying completion")
            doVerifyBatchComplete()
        }
    }

    private fun addToCacheChunkList(fileId: String, partUrls: List<PartUrl>) {
        DebugUtil.d(TAG, "addToCacheChunkList# ${partUrls.size}")
        synchronized(chunkCacheLock) {
            mCacheChunkList.addAll(partUrls.map {
                ChunkBean(
                    fileId,
                    it.partNumber,
                    CHUNK_SIZE,
                    it.signUrl,
                    System.currentTimeMillis(),
                    null,
                    mTotalChunk
                )
            })
        }
    }

    fun doVerifyBatchComplete() {
        if (mCompleteChunkList.size == mTotalChunk) {
            val fileSize = "%.2f".format(mFileSize / FILE_SIZE_MB.toFloat())
            val duration = System.currentTimeMillis() - startUploadTime
            val startTime = System.currentTimeMillis()
            DebugUtil.d(TAG, "doVerifyBatchComplete# completeVerify file-size=$fileSize upload-duration=$duration")
            AudioFileChunkUploader.uploadCompleteVerify(mCompleteChunkList.toList()) { errCode ->
                val duration = System.currentTimeMillis() - startTime
                DebugUtil.d(TAG, "doVerifyBatchComplete# completeVerify duration = $duration")
                when (errCode) {
                    LinkShareMessageHelper.CODE_DOING_SUCCESS -> doComplete()
                    else -> doError(errCode)
                }
            }
        } else {
            clearChunkCache()
            doError(LinkShareMessageHelper.CODE_DIALOG_GENERATE_FAIL)
        }
    }

    private fun clearChunkCache() {
        synchronized(this) {
            mCacheChunkList.clear()
            mCompleteChunkList.clear()
        }
    }

    fun doError(errorCode: Int) {
        synchronized(this) {
            DebugUtil.logStack(TAG, "doError = $errorCode  isDoneError = $isDoneError", 2)
            if (isDoneError || mStatus == STATUS_DESTROYED) {
                return
            }
            isDoneError = true
            targetActivity?.get()?.let {
                mNotificationHelper?.doErrorMessage(this, it, errorCode)
            }
            if (LinkShareMessageHelper.isNeedRetry(errorCode)) {
                mStatus = STATUS_IN_PAUSE
            } else {
                ShareManager.unbindShareLinkService()
            }
        }
    }

    fun doMobileNetworkError(): Boolean {
        if (isMobileNetwork && !isDoneError && mStatus != 0 && mStatus != STATUS_DESTROYED) {
            isMobileNetwork = false
            isDoneError = true
            changeStatus(STATUS_IN_PAUSE)
            val flows = "%.1fMB".format((mFileSize - mUploadFileSize) / FILE_SIZE_MB.toFloat())
            val messageContent = resources.getString(com.soundrecorder.base.R.string.link_share_flow_notification_numbers, flows)
            targetActivity?.get()?.let {
                mNotificationHelper?.doNetWorkError(this, it, LinkShareMessageHelper.CODE_DIALOG_NO_WIFI_CONNECTION, messageContent)
            }
            return true
        }
        return false
    }

    fun doUpdateProgress(updated: Long? = null) {
        synchronized(this) {
            // 错误已经发生，多线程异步上传会回调到这里，所以需要判断是否已经出错
            if (isDoneError || mStatus != STATUS_IN_UPLOAD || mFileSize <= 0) return

            mUploadFileSize = updated?.let { mUploadFileSize + it }
                ?: (mCompleteChunkList.size * CHUNK_SIZE)

            targetActivity?.get()?.let { activity ->
                mNotificationHelper?.let { notificationHelper ->
                    val progress = ((mUploadFileSize.toDouble() / mFileSize) * PROGRESS_UNIT).toInt()
                        .coerceAtMost(PROGRESS_UNIT)
                    notificationHelper.doUpdateProgress(this, activity, progress)
                }
            }
        }
    }

    fun doComplete() {
        val notificationHelper = mNotificationHelper
        val activity = targetActivity?.get()
        if (notificationHelper != null && activity != null) {
            notificationHelper.doCompleted(this, activity)
        }
        mRegisterResponse?.let {
            linkShareListener.onLinkShareComplete(shareTextContent.mediaRecordId, it.url)
        }
    }
}


/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : NSPlayer.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-25
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */
package com.soundrecorder.nsplayer

import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.PlaybackParams
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.soundrecorder.effect.api.AudioEffect
import java.io.FileDescriptor
import java.io.IOException
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import kotlin.concurrent.Volatile
import kotlin.math.max
import kotlin.math.min

class NSPlayer(context: Context) {
    private val mPauseLock = Object()
    private val mSeekLock = Object()
    private val mEngineLock = Object()
    private val mContext: Context
    private val mSeekToPositionUs = AtomicLong(DEFAULT_SEEK_STATE)
    private val mMainHandler = Handler(Looper.getMainLooper())
    private var mExecutor: ExecutorService? = null
    private var mPlaybackFuture: Future<*>? = null
    private var mFadeInFuture: Future<*>? = null

    private var mNSEngine: AudioEffect? = null
    private var mMediaExtractor: MediaExtractor? = null
    private var mMediaCodec: MediaCodec? = null
    private var mAudioTrack: AudioTrack? = null

    @Volatile
    private var mNSEnabled = false
    @Volatile
    private var mUsingMicrophone = false
    @Volatile
    private var mSampleRate = -1
    @Volatile
    private var mChannelCount = -1
    @Volatile
    private var mBitWidth = -1
    @Volatile
    private var mDuration = 0L
    @Volatile
    private var mPlaySpeed = 1.0f
    @Volatile
    private var mAudioStreamType = AudioManager.STREAM_MUSIC
    @Volatile
    private var mPlayerState = STATE_IDLE

    private var mIsPlaying = AtomicBoolean(false)
    private var mIsPausing = AtomicBoolean(false)
    private var mSeekChangedDecoder = AtomicBoolean(false)
    private var mOnErrorListener: OnErrorListener? = null

    private var mOnPreparedListener: OnPreparedListener? = null
    private var mOnSeekCompleteListener: OnSeekCompleteListener? = null
    private var mOnCompletionListener: OnCompletionListener? = null
    private var mAudioBufferPool: FixedBufferPool? = null

    /**
     * The size needed by the audio effect processor, 20ms buffer frame by default
     */
    private var mProcessBufferBytes = -1
    private var mDecodeBuffer: ByteArray? = null
    private var mProcessPartialBuffer: ByteArray? = null
    private var mProcessPartialSize = 0
    private var mPcmWriter: PcmWriter? = null

    init {
        mPlayerState = STATE_IDLE
        mMediaExtractor = MediaExtractor()
        mContext = context.applicationContext
        if (DEBUG_NS_PLAYER) {
            mPcmWriter = PcmWriter(mContext)
        }
        Log.i(TAG, "debug flag: $DEBUG_NS_PLAYER")
    }

    interface OnErrorListener {
        fun onError(what: Int): Boolean
    }

    interface OnPreparedListener {
        fun onPrepared(): Boolean
    }

    interface OnSeekCompleteListener {
        fun onSeekComplete(): Boolean
    }

    interface OnCompletionListener {
        fun onCompletion()
    }

    fun setOnErrorListener(listener: OnErrorListener?) {
        mOnErrorListener = listener
    }

    fun setOnCompletionListener(listener: OnCompletionListener?) {
        mOnCompletionListener = listener
    }

    fun setOnPreparedListener(listener: OnPreparedListener?) {
        mOnPreparedListener = listener
    }

    fun setOnSeekCompleteListener(listener: OnSeekCompleteListener?) {
        mOnSeekCompleteListener = listener
    }

    fun setNSEngine(engine: AudioEffect?) {
        synchronized(mEngineLock) {
            mNSEngine = engine
        }
    }

    fun isNSEnabled(): Boolean {
        synchronized(mEngineLock) {
            return mNSEnabled
        }
    }

    fun setNSEnabled(enabled: Boolean) {
        synchronized(mEngineLock) {
            mNSEnabled = enabled
            mNSEngine?.setEnabled(enabled)
        }
    }

    @Throws(IOException::class, IllegalStateException::class)
    fun prepare() {
        // check data source first
        if (mPlayerState != STATE_PREPARING) {
            Log.e(TAG, "Please call prepare() after setDataSource()!")
            throw IllegalStateException("prepare called in invalid state")
        }

        val extractor = mMediaExtractor ?: throw IllegalStateException("MediaExtractor not initialized")
        // select audio track
        val audioTrackIndex = getAudioTrackIndex(extractor)
        if (audioTrackIndex == -1) {
            onErrorCallback(MEDIA_ERROR_MALFORMED)
            throw IOException("No audio track found")
        }

        // get config
        val format = extractor.getTrackFormat(audioTrackIndex)
        extractor.selectTrack(audioTrackIndex)

        mSampleRate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE)
        mChannelCount = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT)
        val channelConfig = if (mChannelCount == 1) AudioFormat.CHANNEL_OUT_MONO else AudioFormat.CHANNEL_OUT_STEREO
        mBitWidth = getAudioBitWidth(format)
        mDuration = getDuration()

        // init AudioTrack
        val minBufferSize = AudioTrack.getMinBufferSize(mSampleRate, channelConfig, AudioFormat.ENCODING_PCM_16BIT)
        mProcessBufferBytes = mSampleRate * mChannelCount * mBitWidth * PROCESS_TIME_UNIT_MS / MILLIS_PER_SECOND / BIT_PER_BYTE
        mAudioBufferPool = FixedBufferPool(BUFFER_POOL_SIZE, mProcessBufferBytes)
        val bufferSize = max((minBufferSize * MAX_PLAY_SPEED).toDouble(), (mProcessBufferBytes * BUFFER_TIMES).toDouble()).toInt()
        Log.d(TAG, ("audio length: " + getDuration() + " , sample rate: " + mSampleRate
                    + " , channel: " + mChannelCount + " , bitWidth: " + mBitWidth
                    + " , buffer: " + minBufferSize + " , pBuffer: " + mProcessBufferBytes))

        mAudioTrack = AudioTrack(
            mAudioStreamType,
            mSampleRate,
            channelConfig,
            getAudioEncoding(format),
            bufferSize,
            AudioTrack.MODE_STREAM
        )

        // set microphone
        if (isUsingMicrophone()) {
            enableMicrophone()
        }

        // init MediaCodec
        val mime = format.getString(MediaFormat.KEY_MIME) ?: throw IOException("Missing MIME type in media format")
        mMediaCodec = MediaCodec.createDecoderByType(mime).apply {
            configure(format, null, null, 0)
        }

        if (DEBUG_NS_PLAYER) {
            mPcmWriter?.prepare(mBitWidth, mChannelCount, mSampleRate)
        }

        mPlayerState = STATE_PREPARED
    }

    private fun getAudioTrackIndex(extractor: MediaExtractor): Int {
        var audioTrackIndex = -1
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            val mime = format.getString(MediaFormat.KEY_MIME)
            if (mime != null && mime.startsWith("audio/")) {
                audioTrackIndex = i
                break
            }
        }
        return audioTrackIndex
    }

    private fun enableMicrophone() {
        val audioManager = mContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
        audioManager.isSpeakerphoneOn = false
    }

    @Throws(IllegalStateException::class)
    fun start() {
        if (mPlayerState != STATE_PREPARED && mPlayerState != STATE_PAUSED && mPlayerState != STATE_COMPLETED) {
            Log.e(TAG, "Please call start() after setDataSource()/prepare()/pause()!")
            throw IllegalStateException("start called in invalid state")
        }

        if (mIsPlaying.get()) {
            return
        }

        mIsPlaying.set(true)
        mIsPausing.set(false)

        if (mPlayerState != STATE_PAUSED) {
            // start decoder
            mMediaCodec?.start()

            mExecutor = Executors.newFixedThreadPool(MAX_THREADS) { r ->
                Thread(r, "NSPlayerThread")
            }
            mPlaybackFuture = mExecutor?.submit {
                decodeAndPlayTask()
            }
        } else {
            synchronized(mPauseLock) {
                mPauseLock.notifyAll()
            }
        }

        mAudioTrack?.play()

        // set speed
        val params = PlaybackParams()
            .setSpeed(mPlaySpeed)
            .setPitch(1.0f)
        try {
            mAudioTrack?.playbackParams = params
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "setPlaybackParams error: " + e.message)
        }

        mPlayerState = STATE_PLAYING
    }

    private fun decodeAndPlayTask() {
        try {
            val info = MediaCodec.BufferInfo()
            val eosFlags = booleanArrayOf(false, false) // [inputEOS, outputEOS]

            while ((!eosFlags[1] && (mIsPlaying.get() || mIsPausing.get())) || mSeekChangedDecoder.get()) {
                if (Thread.interrupted()) {
                    Log.d(TAG, "Playback thread interrupted, exiting")
                    break
                }
                handlePauseState()
                if (!mIsPlaying.get()) {
                    break
                }
                handleSeekState(eosFlags)
                handleInputBuffer(eosFlags)
                handleOutputBuffer(info, eosFlags)
            }

            if (eosFlags[1]) {
                val timeMs = ((mDuration - getCurrentPosition() + DELAY_TIMES_50) / mPlaySpeed).toLong()
                Log.d(TAG, "remain: $timeMs")
                mMainHandler.postDelayed({
                    setState(STATE_COMPLETED)
                }, timeMs)
                mProcessPartialBuffer = null
                mProcessPartialSize = 0
            }

            if (DEBUG_NS_PLAYER) {
                mPcmWriter?.finish()
            }

            Log.d(TAG, "exit task: decode")
        } catch (ex: IllegalStateException) {
            Log.e(TAG, "D&P task error: " + ex.message)
        } catch (ex: InterruptedException) {
            Log.e(TAG, "D&P task error: " + ex.message)
        }
    }

    private fun handlePauseState() {
        synchronized(mPauseLock) {
            while (mIsPausing.get()) {
                try {
                    mPauseLock.wait()
                    Log.d(TAG, "PL released in t1")
                } catch (e: InterruptedException) {
                    Log.e(TAG, "error in pause LK, " + e.message)
                    Thread.currentThread().interrupt()
                }
            }
        }
    }

    private fun handleSeekState(eosFlags: BooleanArray) {
        synchronized(mSeekLock) {
            if (mSeekChangedDecoder.get() && (DEFAULT_SEEK_STATE != mSeekToPositionUs.get())) {
                if (mSeekToPositionUs.get() != 0L && mSeekToPositionUs.get() != DEFAULT_SEEK_STATE) {
                    fadeInVolume(mAudioTrack)
                }

                mMediaExtractor?.seekTo(
                    mSeekToPositionUs.get(),
                    MediaExtractor.SEEK_TO_CLOSEST_SYNC
                )
                mMediaCodec?.flush()
                mSeekChangedDecoder.set(false)

                mAudioTrack?.stop()
                mAudioTrack?.flush()
                mAudioTrack?.play()

                eosFlags[1] = false
                eosFlags[0] = false
            }
        }
    }

    private fun handleInputBuffer(eosFlags: BooleanArray) {
        if (eosFlags[0]) {
            return
        }
        val mediaCodec = mMediaCodec ?: run {
            return
        }
        val mediaExtractor = mMediaExtractor ?: run {
            return
        }

        val inputBufferIndex = mediaCodec.dequeueInputBuffer(MILLIS_PER_SECOND.toLong()) ?: -1
        if (inputBufferIndex >= 0) {
            mediaCodec.getInputBuffer(inputBufferIndex)?.let { inputBuffer ->
                val sampleSize = mediaExtractor.readSampleData(inputBuffer, 0)

                if (sampleSize < 0) {
                    Log.i(TAG, "input EOS")
                    eosFlags[0] = true
                    mediaCodec.queueInputBuffer(
                        inputBufferIndex, 0, 0, 0,
                        MediaCodec.BUFFER_FLAG_END_OF_STREAM
                    )
                } else {
                    val presentationTimeUs = mediaExtractor.sampleTime
                    mediaCodec.queueInputBuffer(
                        inputBufferIndex, 0, sampleSize,
                        presentationTimeUs, 0
                    )
                    mediaExtractor.advance()
                }
            }
        }
    }

    private fun handleOutputBuffer(info: MediaCodec.BufferInfo, eosFlags: BooleanArray) {
        val mediaCodec = mMediaCodec ?: run {
            Log.e(TAG, "MediaCodec is null")
            return
        }
        val outputBufferIndex = mediaCodec.dequeueOutputBuffer(info, MILLIS_PER_SECOND.toLong())
        if (outputBufferIndex >= 0) {
            if ((info.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                Log.i(TAG, "output EOS")
                eosFlags[1] = true
            }

            mediaCodec.getOutputBuffer(outputBufferIndex).let { outputBuffer ->
                val decodeBuffer = mDecodeBuffer.takeIf { it?.size == info.size }
                    ?: ByteArray(info.size).also { mDecodeBuffer = it }

                outputBuffer?.get(decodeBuffer)
                outputBuffer?.clear()

                processAudioTask(decodeBuffer)

                mediaCodec.releaseOutputBuffer(outputBufferIndex, false)
            }
        } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
            val format = mediaCodec.outputFormat
            if (format.containsKey(MediaFormat.KEY_SAMPLE_RATE)) {
                val sampleRate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE)
                Log.d(
                    TAG,
                    "output format: sample rate $sampleRate"
                )
            }
            if (format.containsKey(MediaFormat.KEY_CHANNEL_COUNT)) {
                val channelCount = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT)
                Log.d(TAG, "output format: channel $channelCount")
            }
            if (format.containsKey(MediaFormat.KEY_PCM_ENCODING)) {
                val pcmEncoding = format.getInteger(MediaFormat.KEY_PCM_ENCODING)
                Log.d(TAG, "output format: bit width $pcmEncoding")
            }
        }
    }

    private fun processAudioTask(dataBuffer: ByteArray) {
        try {
            handlePauseState()
            if (!mIsPlaying.get()) {
                return
            }

            val dataIndex = intArrayOf(dataBuffer.size, 0) // [dataSize, offset]
            processPartialBuffer(dataBuffer, dataIndex)
            processFullBuffers(dataBuffer, dataIndex)
            processRemainingData(dataBuffer, dataIndex)
        } catch (ex: IllegalStateException) {
            Log.e(TAG, "process audio task error! " + ex.message)
        } catch (ex: InterruptedException) {
            Log.e(TAG, "process audio task error! " + ex.message)
        }
    }

    private fun processPartialBuffer(dataBuffer: ByteArray, dataIndex: IntArray) {
        val buffer = mProcessPartialBuffer ?: return

        val needed = mProcessBufferBytes - mProcessPartialSize
        val copySize = min(needed.toDouble(), dataIndex[0].toDouble()).toInt()

        System.arraycopy(
            dataBuffer, dataIndex[1],
            buffer, mProcessPartialSize,
            copySize
        )

        mProcessPartialSize += copySize
        dataIndex[1] += copySize
        dataIndex[0] -= copySize

        // check if has full data of 20ms
        if (mProcessPartialSize == mProcessBufferBytes) {
            // apply effect
            val processed = applyEffect(
                buffer, 0, mProcessBufferBytes
            )

            // write AudioTrack
            mAudioTrack?.write(processed, 0, processed.size)
            if (DEBUG_NS_PLAYER) {
                mPcmWriter?.writeChunk(processed)
            }
            mAudioBufferPool?.returnBuffer(processed)

            mAudioBufferPool?.returnBuffer(mProcessPartialBuffer)
            mProcessPartialBuffer = null
            mProcessPartialSize = 0
        } else {
            Log.i(TAG, "dropped length: $mProcessPartialSize")
        }
    }

    private fun processFullBuffers(dataBuffer: ByteArray, dataIndex: IntArray) {
        while (dataIndex[0] >= mProcessBufferBytes) {
            // apply effect
            val processed = applyEffect(
                dataBuffer, dataIndex[1], mProcessBufferBytes
            )

            // write AudioTrack
            mAudioTrack?.write(processed, 0, mProcessBufferBytes)
            if (DEBUG_NS_PLAYER) {
                mPcmWriter?.writeChunk(processed)
            }
            mAudioBufferPool?.returnBuffer(processed)

            // update position
            dataIndex[1] += mProcessBufferBytes
            dataIndex[0] -= mProcessBufferBytes
        }
    }

    private fun processRemainingData(dataBuffer: ByteArray, dataIndex: IntArray) {
        if (dataIndex[0] <= 0) {
            return
        }
        val targetBuffer = mProcessPartialBuffer ?: run {
            mAudioBufferPool?.getBuffer()?.also {
                mProcessPartialBuffer = it
                mProcessPartialSize = 0
            } ?: return
        }

        System.arraycopy(
            dataBuffer, dataIndex[1],
            targetBuffer, mProcessPartialSize,
            dataIndex[0]
        )
        mProcessPartialSize += dataIndex[0]
    }

    private fun applyEffect(data: ByteArray, index: Int, size: Int): ByteArray {
        synchronized(mEngineLock) {
            val source = mAudioBufferPool?.getBuffer() ?: run {
                Log.e(TAG, "Failed to get source buffer from pool")
                return data
            }
            val result = mAudioBufferPool?.getBuffer() ?: run {
                mAudioBufferPool?.returnBuffer(source)
                Log.e(TAG, "Failed to get result buffer from pool")
                return data
            }

            System.arraycopy(data, index, source, 0, size)

            // the algo wants all data send to the library even if the function is disabled
            if ((mNSEngine != null)) {
                val startTime = System.currentTimeMillis()
                try {
                    mNSEngine?.process(source, result)
                } catch (ex: IllegalStateException) {
                    Log.e(TAG, "ns process error! " + ex.message)
                    System.arraycopy(source, 0, result, 0, size)
                } catch (ex: InterruptedException) {
                    Log.e(TAG, "ns process error! " + ex.message)
                    System.arraycopy(source, 0, result, 0, size)
                }
                val endTime = System.currentTimeMillis()

                if (DEBUG_NS_PLAYER) {
                    Log.d(TAG, "ns process time: " + (endTime - startTime))
                }
            } else {
                System.arraycopy(source, 0, result, 0, size)
            }
            mAudioBufferPool?.returnBuffer(source)
            return result
        }
    }

    fun pause() {
        if (!mIsPlaying.get() || (mPlayerState != STATE_PLAYING)) {
            Log.w(TAG, "Calling pause() when not playing!")
            return
        }
        Log.d(TAG, "pause")
        mIsPlaying.set(false)
        mIsPausing.set(true)
        mAudioTrack?.pause()
        setState(STATE_PAUSED)
    }

    fun isPlaying(): Boolean {
        return mIsPlaying.get()
    }

    @Synchronized
    fun stop() {
        if ((mPlayerState != STATE_PREPARED)
            && (mPlayerState != STATE_PAUSED)
            && (mPlayerState != STATE_COMPLETED)
            && (mPlayerState != STATE_PLAYING)
        ) {
            Log.w(TAG, "Calling stop() when not paused/playing!")
            return
        }
        Log.d(TAG, "stop")

        mMainHandler.removeCallbacksAndMessages(null)

        mIsPlaying.set(false)
        mIsPausing.set(false)
        mSeekToPositionUs.set(DEFAULT_SEEK_STATE)

        synchronized(mPauseLock) {
            mPauseLock.notifyAll()
        }

        exitThread()

        // clear resource
        mAudioTrack?.apply {
            stop()
            flush()
        }

        mMediaExtractor?.release()
        mMediaExtractor = null

        mAudioBufferPool?.clear()
        mAudioBufferPool = null

        setState(STATE_STOPPED)
    }

    private fun exitThread() {
        mFadeInFuture?.let { future ->
            try {
                if (!future.isDone) {
                    future.cancel(true)
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "Error cancelling fade in future")
                Thread.currentThread().interrupt()
            } finally {
                mFadeInFuture = null
            }
        }

        mPlaybackFuture?.let { future ->
            try {
                if (!future.isDone && !future.isCancelled) {
                    future.cancel(true)
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "Error cancelling playback future")
                Thread.currentThread().interrupt()
            } finally {
                mPlaybackFuture = null
            }
        }

        mExecutor?.let { executor ->
            executor.shutdownNow()
            try {
                if (!executor.awaitTermination(THREAD_JOIN_TIMES, TimeUnit.MILLISECONDS)) {
                    Log.w(TAG, "Executor did not terminate in time")
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "Executor shutdown interrupted", e)
                Thread.currentThread().interrupt()
            } finally {
                mExecutor = null
            }
        }
    }

    @Synchronized
    fun release() {
        Log.d(TAG, "release")
        stop()

        mMediaCodec?.let {
            try {
                it.stop()
            } catch (ex: IllegalStateException) {
                Log.e(TAG, "mediacodec stop error")
            } catch (ex: InterruptedException) {
                Log.e(TAG, "mediacodec stop error")
            } finally {
                it.release()
            }
            mMediaCodec = null
        }

        mAudioTrack?.let {
            it.release()
            mAudioTrack = null
        }

        mSampleRate = -1
        mChannelCount = -1
        mBitWidth = -1
        mDuration = 0
        mDecodeBuffer = null

        synchronized(mEngineLock) {
            mNSEngine = null
        }

        setState(STATE_IDLE)
    }

    fun getDuration(): Long {
        mMediaExtractor?.let {
            for (i in 0 until it.trackCount) {
                val format = it.getTrackFormat(i)
                if (format.containsKey(MediaFormat.KEY_DURATION)) {
                    return format.getLong(MediaFormat.KEY_DURATION) / MILLIS_PER_SECOND // ms
                }
            }
        }
        return 0
    }

    fun reset() {
        Log.d(TAG, "reset")
        stop()
        release()
    }

    fun isUsingMicrophone(): Boolean {
        return mUsingMicrophone
    }

    /**
     * Gets the current playback position.
     *
     * @return the current position in milliseconds
     */
    fun getCurrentPosition(): Long {
        var result = 0L
        mAudioTrack?.let {
            // if seeking, return the seek position
            if (mSeekChangedDecoder.get()) {
                result = mSeekToPositionUs.get() / MILLIS_PER_SECOND
            } else {
                try {
                    // if not seeking, return the current position
                    val playedFrames = it.playbackHeadPosition
                    // convert to ms
                    result = if (getState() == STATE_COMPLETED && mDuration != 0L) {
                        mDuration
                    } else if (mSeekToPositionUs.get() == DEFAULT_SEEK_STATE) {
                        playedFrames.toLong() * MILLIS_PER_SECOND / mSampleRate
                    } else {
                        mSeekToPositionUs.get() / MILLIS_PER_SECOND + (playedFrames.toLong() * MILLIS_PER_SECOND / mSampleRate)
                    }
                } catch (e: IllegalStateException) {
                    Log.e(TAG, "get position error")
                }
            }
        }
        return result
    }

    fun setVolume(volume: Float) {
        mAudioTrack?.setVolume(volume)
    }

    fun setSpeed(speed: Float) {
        if ((mPlaySpeed < 0) || (mPlaySpeed > MAX_PLAY_SPEED)) {
            return
        }
        mPlaySpeed = speed
        if (mPlayerState == STATE_PLAYING) {
            pause()
            start()
        }
    }

    fun setAudioStreamType(streamtype: Int) {
        mAudioStreamType = streamtype
    }

    @Throws(IllegalStateException::class)
    fun seekTo(msec: Int) {
        if (mPlayerState == STATE_IDLE || mPlayerState == STATE_ERROR) {
            Log.e(TAG, "Cannot seek in IDLE or ERROR")
            throw IllegalStateException("Cannot seek in state: $mPlayerState")
        }
        Log.d(TAG, "seekTo $msec")

        synchronized(mSeekLock) {
            mSeekToPositionUs.set(msec.toLong() * MILLIS_PER_SECOND)
            if (DEFAULT_SEEK_STATE != mSeekToPositionUs.get()) {
                mSeekChangedDecoder.set(true)
            }
        }
    }

    private fun notifySeekComplete() {
        mOnSeekCompleteListener?.let {
            mMainHandler.post {
                it.onSeekComplete()
            }
        }
    }

    private fun checkDecoder() {
        if (mMediaExtractor == null) {
            mMediaExtractor = MediaExtractor()
        }
    }

    @Throws(IOException::class)
    fun setDataSource(path: String) {
        checkDecoder()
        mMediaExtractor?.setDataSource(path)
        setState(STATE_PREPARING)
    }

    @Throws(IOException::class)
    fun setDataSource(fd: FileDescriptor, offset: Long, length: Long) {
        checkDecoder()
        mMediaExtractor?.setDataSource(fd, offset, length)
        setState(STATE_PREPARING)
    }

    @Throws(IOException::class)
    fun setDataSource(fd: FileDescriptor) {
        checkDecoder()
        mMediaExtractor?.setDataSource(fd)
        setState(STATE_PREPARING)
    }

    @Throws(IOException::class, IllegalArgumentException::class, IllegalStateException::class)
    fun setDataSource(afd: AssetFileDescriptor) {
        checkDecoder()
        mMediaExtractor?.setDataSource(afd)
        setState(STATE_PREPARING)
    }

    @Throws(IOException::class)
    fun setDataSource(uri: Uri) {
        checkDecoder()
        mMediaExtractor?.setDataSource(mContext, uri, null)
        setState(STATE_PREPARING)
    }

    private fun getAudioBitWidth(format: MediaFormat): Int {
        if (format.containsKey(MediaFormat.KEY_PCM_ENCODING)) {
            val encoding = format.getInteger(MediaFormat.KEY_PCM_ENCODING)
            return getBitWidthFromEncoding(encoding)
        }

        // default 16 bits
        return BIT_WIDTH_16
    }

    private fun getAudioEncoding(format: MediaFormat): Int {
        if (format.containsKey(MediaFormat.KEY_PCM_ENCODING)) {
            return format.getInteger(MediaFormat.KEY_PCM_ENCODING)
        }
        return AudioFormat.ENCODING_PCM_16BIT
    }

    private fun getBitWidthFromEncoding(encoding: Int): Int {
        return when (encoding) {
            AudioFormat.ENCODING_PCM_8BIT -> BIT_WIDTH_8
            AudioFormat.ENCODING_PCM_16BIT -> BIT_WIDTH_16
            AudioFormat.ENCODING_PCM_24BIT_PACKED -> BIT_WIDTH_24
            AudioFormat.ENCODING_PCM_32BIT, AudioFormat.ENCODING_PCM_FLOAT -> BIT_WIDTH_32
            else -> BIT_WIDTH_16
        }
    }

    fun getState(): Int {
        return mPlayerState
    }

    fun setState(state: Int) {
        mPlayerState = state
        Log.d(TAG, "set state: $state")

        when (state) {
            STATE_PREPARED -> {
                mMainHandler.post {
                    mOnPreparedListener?.onPrepared()
                }
            }

            STATE_COMPLETED -> {
                mMainHandler.post {
                    mOnCompletionListener?.onCompletion()
                }
            }

            else -> {}
        }
    }

    fun getBitWidth(): Int {
        return mBitWidth
    }

    fun getSampleRate(): Int {
        return mSampleRate
    }

    fun getChannelCount(): Int {
        return mChannelCount
    }

    private fun onErrorCallback(errorCode: Int) {
        mOnErrorListener?.onError(errorCode)
    }

    @Synchronized
    private fun fadeInVolume(audioTrack: AudioTrack?) {
        val stepTime = (FADE_IN_TIME / FADE_IN_STEPS).toLong()
        val delta = 1.0f / FADE_IN_STEPS
        mFadeInFuture?.let {
            if (!it.isDone) {
                it.cancel(true)
            }
        }
        audioTrack?.setVolume(0f)
        mFadeInFuture = mExecutor?.submit {
            try {
                var vol = 0f
                for (i in 0 until FADE_IN_STEPS) {
                    Thread.sleep(stepTime)

                    vol = min(1.0, (vol + delta).toDouble()).toFloat()
                    audioTrack?.setVolume(vol)
                }
            } catch (ex: InterruptedException) {
                Log.e(TAG, "ignored")
                Thread.currentThread().interrupt()
            }
        }
    }

    companion object {
        const val MEDIA_ERROR_IO: Int = -1
        const val MEDIA_ERROR_MALFORMED: Int = -2
        const val MEDIA_ERROR_UNSUPPORTED: Int = -3
        const val MEDIA_ERROR_TIMEOUT: Int = -4

        const val STATE_IDLE: Int = 0
        const val STATE_PREPARING: Int = 1
        const val STATE_PREPARED: Int = 2
        const val STATE_PLAYING: Int = 3
        const val STATE_PAUSED: Int = 4
        const val STATE_STOPPED: Int = 5
        const val STATE_COMPLETED: Int = 6
        const val STATE_ERROR: Int = -1

        private const val TAG = "NSPlayer"

        // adb shell setprop log.tag.NSPlayer DEBUG
        private val DEBUG_NS_PLAYER = Log.isLoggable(TAG, Log.DEBUG)
        private const val PROCESS_TIME_UNIT_MS = 20
        private const val DEFAULT_SEEK_STATE = -1000L
        private const val MILLIS_PER_SECOND = 1000
        private const val BIT_PER_BYTE = 8
        private const val BIT_WIDTH_8 = 8
        private const val BIT_WIDTH_16 = 16
        private const val BIT_WIDTH_24 = 24
        private const val BIT_WIDTH_32 = 32
        private const val BUFFER_POOL_SIZE = 10
        private const val BUFFER_TIMES = 4
        private const val THREAD_JOIN_TIMES: Long = 300
        private const val DELAY_TIMES_50 = 50

        private const val MAX_PLAY_SPEED = 2
        private const val MAX_THREADS = 2
        private const val FADE_IN_STEPS = 4
        private const val FADE_IN_TIME = 1000
    }
}
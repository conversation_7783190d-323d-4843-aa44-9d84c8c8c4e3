/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : AudioBufferPool.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-17
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */
package com.soundrecorder.nsplayer

import java.util.LinkedList
import java.util.Queue

class FixedBufferPool(poolSize: Int, private val mBufferSize: Int) {
    private val mBufferQueue: Queue<ByteArray> = LinkedList()

    init {
        for (i in 0 until poolSize) {
            mBufferQueue.offer(ByteArray(mBufferSize))
        }
    }

    @Synchronized
    fun getBuffer(): ByteArray {
        val buffer = mBufferQueue.poll()
        return buffer ?: ByteArray(mBufferSize)
    }

    @Synchronized
    fun returnBuffer(buffer: ByteArray?) {
        if (buffer != null && buffer.size == mBufferSize) {
            mBufferQueue.offer(buffer)
        }
    }

    @Synchronized
    fun clear() {
        mBufferQueue.clear()
    }

    companion object {
        private const val TAG = "FBPool"
    }
}

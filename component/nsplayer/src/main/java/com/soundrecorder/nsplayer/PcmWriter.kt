/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : PcmWriter.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-23
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.nsplayer

import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import java.io.BufferedOutputStream
import java.io.IOException
import java.io.OutputStream

class PcmWriter(private val context: Context) {

    companion object {
        private const val TAG: String = "PcmWriter"
        private const val BUFFER_SIZE: Int = 8192
    }
    private var outputStream: OutputStream? = null
    private var bufferedStream: BufferedOutputStream? = null
    private var targetUri: Uri? = null

    fun prepare(bitWidth: Int, channel: Int, sampleRate: Int) {
        val resolver = context.contentResolver
        val fileName = "sound_recorder_ns_${bitWidth}_${channel}_$sampleRate.pcm"
        val relativePath = Environment.DIRECTORY_DOWNLOADS
        val mimeType = "application/octet-stream"

        // try to delete old one
        val existingUri = resolver.query(
            MediaStore.Downloads.EXTERNAL_CONTENT_URI,
            arrayOf(MediaStore.Downloads._ID),
            "${MediaStore.Downloads.DISPLAY_NAME}=? AND ${MediaStore.Downloads.RELATIVE_PATH}=?",
            arrayOf(fileName, "$relativePath/"),
            null
        )?.use { cursor ->
            if (cursor.moveToFirst()) {
                val id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Downloads._ID))
                ContentUris.withAppendedId(MediaStore.Downloads.EXTERNAL_CONTENT_URI, id)
            } else null
        }

        existingUri?.let {
            resolver.delete(it, null, null)
        }

        // create new one
        val contentValues = ContentValues().apply {
            put(MediaStore.Downloads.DISPLAY_NAME, fileName)
            put(MediaStore.Downloads.MIME_TYPE, mimeType)
            put(MediaStore.Downloads.RELATIVE_PATH, relativePath)
            put(MediaStore.Downloads.IS_PENDING, 1)
        }

        val uri = resolver.insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues)
        targetUri = uri

        // open output stream
        outputStream = uri?.let { resolver.openOutputStream(it, "w") }
        bufferedStream = outputStream?.let { BufferedOutputStream(it, BUFFER_SIZE) }

        Log.i(TAG, "prepare")
    }

    fun writeChunk(pcmChunk: ByteArray) {
        bufferedStream?.write(pcmChunk)
    }

    fun finish() {
        try {
            bufferedStream?.flush()
        } catch (e: IOException) {
            Log.e(TAG, "flush error", e)
        } finally {
            try {
                bufferedStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "close error", e)
            }
            bufferedStream = null
            outputStream = null
        }

        targetUri?.let {
            val values = ContentValues().apply {
                put(MediaStore.Downloads.IS_PENDING, 0)
            }
            context.contentResolver.update(it, values, null, null)
        }

        Log.i(TAG, "finish")
    }
}
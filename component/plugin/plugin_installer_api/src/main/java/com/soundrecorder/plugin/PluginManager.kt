/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : PluginManager.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-26
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin

import java.io.File

object PluginManager {
    const val PLUGIN_INSTALLER_NS_LOCAL = "plugin_installer_ns_local"
    const val PLUGIN_INSTALLER_NS_CLOUD = "plugin_installer_ns_cloud"
    const val PLUGIN_INSTALLER_NS = "plugin_installer_ns"
    const val PLUGIN_NAME_NS = "plugin_oplus_ns"
    const val NS_ENGINE_CLASS_NAME = "com.soundrecorder.oplusns.NSEngine"
    const val NS_ENGINE_METHOD_COPY_MODELS = "copyModelsToPathSync"
    const val NS_ENGINE_METHOD_COPY_PARAMETER = "copyParameterToPathSync"

    val NS_ENGINE_MODEL_PATH = File.separator + "ns_models"
    val NS_ENGINE_PARAMETER_PATH = File.separator + "ns_parameter"

    private val mPluginInstallerMap: MutableMap<String, PluginInstaller> = mutableMapOf()

    fun registerPluginInstaller(installerName: String, pluginInstaller: PluginInstaller) {
        mPluginInstallerMap[installerName] = pluginInstaller
    }

    fun getPluginInstaller(installerName: String): PluginInstaller? {
        return mPluginInstallerMap[installerName]
    }

    fun isPluginInstallerEnabled(installerName: String): Boolean {
        return mPluginInstallerMap.containsKey(installerName)
    }
}
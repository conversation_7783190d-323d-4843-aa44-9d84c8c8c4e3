/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : iInstallCallback.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-26
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin

interface InstallCallback {
    companion object {
        const val UNKNOWN: Int = 0
        const val PENDING: Int = 1
        const val REQUIRES_USER_CONFIRMATION: Int = 8
        const val DOWNLOADING: Int = 2
        const val DOWNLOADED: Int = 3
        const val INSTALLING: Int = 4
        const val INSTALLED: Int = 5
        const val FAILED: Int = 6
        const val CANCELING: Int = 9
        const val CANCELED: Int = 7
    }
    fun onStateUpdate(state: Int)
}
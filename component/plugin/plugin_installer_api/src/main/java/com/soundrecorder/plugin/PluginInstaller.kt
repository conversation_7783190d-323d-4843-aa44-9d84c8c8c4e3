/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : iPluginInstaller.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-26
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin

interface PluginInstaller {

    fun getInstallerName(): String

    fun install(showUI: <PERSON><PERSON><PERSON>)

    fun cancel()

    fun unload()

    fun registerCallback(callback: InstallCallback)

    fun getStatus(): Int

    fun getVersion(): Long

    fun getClassLoader(): ClassLoader?

    fun needInstall(): <PERSON><PERSON><PERSON>
}
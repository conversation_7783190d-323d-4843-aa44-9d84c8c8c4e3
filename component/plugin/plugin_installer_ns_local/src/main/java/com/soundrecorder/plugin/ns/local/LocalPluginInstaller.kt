/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : LocalPluginInstaller.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-28
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.local

import android.app.Application
import android.content.Context
import android.util.Log
import com.google.android.play.core.splitinstall.SplitInstallManager
import com.google.android.play.core.splitinstall.SplitInstallManagerFactory
import com.google.android.play.core.splitinstall.SplitInstallRequest
import com.google.android.play.core.splitinstall.SplitInstallSessionState
import com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
import com.google.android.play.core.splitinstall.model.SplitInstallSessionStatus
import com.oplus.oms.split.full.common.NetworkUtil
import com.oplus.oms.split.full.core.Oms
import com.oplus.oms.split.full.core.SplitConfiguration
import com.oplus.oms.split.full.splitload.SplitLoad
import com.soundrecorder.plugin.InstallCallback
import com.soundrecorder.plugin.PluginManager
import com.soundrecorder.plugin.PluginInstaller
import java.io.File

object LocalPluginInstaller : PluginInstaller, SplitInstallStateUpdatedListener {
    private const val TAG = "LPInstaller"

    private lateinit var mSplitInstallManager: SplitInstallManager
    private lateinit var mContext: Application
    private var mInstallCallback: InstallCallback? = null
    private var mTaskId: Int = -1
    private var mNewDownload: Boolean = false

    fun attachBaseContext(ctx: Context) {
        kotlin.runCatching {
            Log.d(TAG, "attachBaseContext")
            mContext = ctx as Application
            val configuration = SplitConfiguration.newBuilder()
                .netWorkingType(NetworkUtil.CLOSE)
                .queryStartUp(false) // set if oms should query cloud feature and navi feature when app just startup
                .splitLoadMode(SplitLoad.MULTIPLE_CLASSLOADER) // setup class loader mode while load feature apk
                .workProcesses(null) // set processes those install feature apk, if one set, it should not preload feature apk
                .localFirst(false) // setup local first strategy, if true means even cloud's verCode > local's use local
                .customProvider(null) // setup provider for custom oms json file
                .build()
            Oms.onAttachBaseContext(ctx, configuration)
        }.onFailure {
            Log.e(TAG, "attachBaseContext error is ${it.message}")
        }
    }

    fun onCreate(application: Application) {
        kotlin.runCatching {
            Log.d(TAG, "onCreate")
            Oms.onApplicationCreate(application)
            mSplitInstallManager = SplitInstallManagerFactory.create(application)
        }.onFailure {
            Log.e(TAG, "onFailure error is ${it.message}")
        }
    }

    override fun getInstallerName(): String {
        return PluginManager.PLUGIN_INSTALLER_NS
    }

    override fun install(showUI: Boolean) {
        Log.i(TAG, "start install OplusNS")

        Log.i(TAG, "local version: ${getVersion()}")

        mSplitInstallManager.unregisterListener(this)
        mSplitInstallManager.registerListener(this)
        mSplitInstallManager.startInstall(SplitInstallRequest.newBuilder().addModule(PluginManager.PLUGIN_NAME_NS).build()).addOnFailureListener {
            mTaskId = -1
            Log.w(TAG, "startInstall Failure is ${it.message}")
        }.addOnSuccessListener {
            mTaskId = it
            Log.d(TAG, "startInstall Prepare for success")
        }
    }

    override fun cancel() {
        if (mTaskId != -1) {
            mSplitInstallManager.cancelInstall(mTaskId)
        }
    }

    override fun unload() {
        Oms.unloadSplit(mContext, PluginManager.PLUGIN_NAME_NS)
    }

    override fun registerCallback(callback: InstallCallback) {
        mInstallCallback = callback
    }

    override fun getStatus(): Int {
        return -1
    }

    override fun getVersion(): Long {
        return Oms.getSplitVersionCode(mContext, PluginManager.PLUGIN_NAME_NS).toLong()
    }

    override fun onStateUpdate(state: SplitInstallSessionState) {
        Log.d(TAG, "state session = " + state.sessionId() + " status = " + state.status())

        when (state.status()) {
            SplitInstallSessionStatus.REQUIRES_USER_CONFIRMATION -> Log.i(TAG, "state user confirmation")

            SplitInstallSessionStatus.INSTALLED -> {
                Log.i(TAG, "state installed")
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.INSTALLED)

                if (mNewDownload) {
                    copyPluginModels(File(mContext.filesDir, PluginManager.NS_ENGINE_MODEL_PATH))
                    copyPluginParameter(File(mContext.filesDir, PluginManager.NS_ENGINE_PARAMETER_PATH))
                    mNewDownload = false
                }
                Log.i(TAG, "installed version: ${getVersion()}")
            }

            SplitInstallSessionStatus.DOWNLOADING -> {
                Log.d(TAG, "state downloading")
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.DOWNLOADING)
            }

            SplitInstallSessionStatus.DOWNLOADED -> {
                Log.i(TAG, "state downloaded")
                mNewDownload = true
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.DOWNLOADED)
            }

            SplitInstallSessionStatus.FAILED -> {
                Log.d(TAG, "state failed" + state.errorCode())
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.FAILED)
            }

            SplitInstallSessionStatus.CANCELED -> {
                Log.d(TAG, "state canceled")
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.CANCELED)
            }
        }
    }

    override fun getClassLoader(): ClassLoader? {
        return Oms.getSplitClassLoader(PluginManager.PLUGIN_NAME_NS)
    }

    override fun needInstall(): Boolean {
        return Oms.getSplitVersionCode(mContext, PluginManager.PLUGIN_NAME_NS) < 0
    }

    private fun copyPluginModels(targetDir: File) {
        runCatching {
            val loader = getClassLoader() ?: return
            val cls = loader.loadClass(PluginManager.NS_ENGINE_CLASS_NAME)
            val instance = cls.getConstructor().newInstance()
            val method = cls.getDeclaredMethod(PluginManager.NS_ENGINE_METHOD_COPY_MODELS, Context::class.java, File::class.java)
            method.invoke(instance, mContext, targetDir)
        }.onFailure {
            Log.e(TAG, "copy models failed", it)
        }
    }

    private fun copyPluginParameter(targetDir: File) {
        runCatching {
            val loader = getClassLoader() ?: return
            val cls = loader.loadClass(PluginManager.NS_ENGINE_CLASS_NAME)
            val instance = cls.getConstructor().newInstance()
            val method = cls.getDeclaredMethod(PluginManager.NS_ENGINE_METHOD_COPY_PARAMETER, Context::class.java, File::class.java)
            method.invoke(instance, mContext, targetDir)
        }.onFailure {
            Log.e(TAG, "copy parameter failed", it)
        }
    }
}
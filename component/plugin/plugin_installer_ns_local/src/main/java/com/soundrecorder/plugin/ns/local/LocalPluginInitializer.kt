/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : LocalPluginInitializer.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-28
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.local

import android.app.Application
import android.content.Context
import android.util.Log
import androidx.startup.Initializer
import com.soundrecorder.plugin.PluginManager

class LocalPluginInitializer : Initializer<Boolean> {
    companion object {
        const val TAG = "LPInitializer"
    }

    override fun create(context: Context): Boolean {
        Log.d(TAG, "create l plugin")
        (context as? Application)?.let {
            LocalPluginInstaller.attachBaseContext(it)
            LocalPluginInstaller.onCreate(it)
            PluginManager.registerPluginInstaller(LocalPluginInstaller.getInstallerName(), LocalPluginInstaller)
        }
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}
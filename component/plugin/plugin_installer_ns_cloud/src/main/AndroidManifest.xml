<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <application>
        <activity
            android:name=".dialog.UserConfirmActivity"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustNothing"
            android:theme="@style/TransparentActivityTheme"/>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="com.soundrecorder.plugin.ns.cloud.CloudPluginInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>
/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : LocalPluginInstaller.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-28
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.cloud

import android.app.Application
import android.content.Context
import android.content.Intent
import android.util.Log
import com.google.android.play.core.splitinstall.SplitInstallException
import com.google.android.play.core.splitinstall.SplitInstallManager
import com.google.android.play.core.splitinstall.SplitInstallManagerFactory
import com.google.android.play.core.splitinstall.SplitInstallRequest
import com.google.android.play.core.splitinstall.SplitInstallSessionState
import com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
import com.google.android.play.core.splitinstall.model.SplitInstallErrorCode
import com.google.android.play.core.splitinstall.model.SplitInstallSessionStatus
import com.oplus.ocs.oms.downloader.OmsDownloader
import com.oplus.ocs.oms.downloader.SplitUpdateManager
import com.oplus.ocs.oms.downloader.columbus.ColumbusDownloadRequestBuilder
import com.oplus.ocs.oms.downloader.columbus.ColumbusResponseParser
import com.oplus.ocs.oms.downloader.constant.NetWorkType
import com.oplus.ocs.oms.downloader.constant.UrlType
import com.oplus.oms.split.full.common.NetworkUtil
import com.oplus.oms.split.full.core.Oms
import com.oplus.oms.split.full.core.SplitConfiguration
import com.oplus.oms.split.full.splitdownload.SplitUpdateInfo
import com.oplus.oms.split.full.splitload.SplitLoad
import com.soundrecorder.plugin.InstallCallback
import com.soundrecorder.plugin.PluginManager
import com.soundrecorder.plugin.PluginInstaller
import com.soundrecorder.plugin.ns.cloud.dialog.UserConfirmActivity
import java.io.File

object CloudPluginInstaller : PluginInstaller, SplitInstallStateUpdatedListener {
    private const val TAG = "CPInstaller"
    private const val UPDATE_TIME_BY_HOURS = 12
    private const val DOWNLOAD_SIZE_THRESHOLD_FOR_MOBILE_DATA = 100 * 1024 * 1024L

    private lateinit var mSplitInstallManager: SplitInstallManager
    private lateinit var mContext: Application
    private lateinit var mUpdateManager: SplitUpdateManager
    private var mInstallCallback: InstallCallback? = null
    private var mTaskId: Int = -1
    private var mNewDownload: Boolean = false
    private val mProgressListeners = mutableListOf<ProgressListener>()

    interface ProgressListener {
        fun onProgress(bytesDownloaded: Long, totalBytesToDownload: Long)
        fun onSuccess()
        fun onFailure(error: Int)
    }

    fun registerProgressListener(listener: ProgressListener) {
        mProgressListeners.add(listener)
    }

    fun unregisterProgressListener(listener: ProgressListener?) {
        listener?.let { mProgressListeners.remove(listener) }
    }

    fun attachBaseContext(ctx: Context) {
        kotlin.runCatching {
            Log.d(TAG, "attachBaseContext")
            mContext = ctx as Application
            initDependencies(ctx)
//            val url = if (BuildConfig.DEBUG) UrlType.CN_TEST else UrlType.CN_MASTER
            val url = UrlType.CN_TEST
            val builder = ColumbusDownloadRequestBuilder(url)
            val parse = ColumbusResponseParser()
            mUpdateManager = SplitUpdateManager(ctx, builder, parse)
            builder.setUpdateManager(mUpdateManager)

            val configuration = SplitConfiguration.newBuilder()
                .downloader(OmsDownloader(DOWNLOAD_SIZE_THRESHOLD_FOR_MOBILE_DATA, false))
                .updateManager(mUpdateManager)
                .netWorkingType(NetworkUtil.NETWORK_NORMAL)
                .queryStartUp(true) // set if oms should query cloud feature and navi feature when app just startup
                .splitLoadMode(SplitLoad.MULTIPLE_CLASSLOADER) // setup class loader mode while load feature apk
                .workProcesses(null) // set processes those install feature apk, if one set, it should not preload feature apk
                .localFirst(false) // setup local first strategy, if true means even cloud's verCode > local's use local
                .customProvider(null) // setup provider for custom oms json file
                .updateTimeByHours(UPDATE_TIME_BY_HOURS)
                .build()
            Oms.onAttachBaseContext(ctx, configuration)
        }.onFailure {
            Log.e(TAG, "attachBaseContext error is ${it.message}")
        }
    }

    fun onCreate(application: Application) {
        kotlin.runCatching {
            Log.d(TAG, "onCreate")
            if (!UserConsentManager.isUserAllowed()) {
                Oms.setNetworkStrategy(NetWorkType.CLOSE)
            }
            Oms.onApplicationCreate(application)
            mSplitInstallManager = SplitInstallManagerFactory.create(application)
        }.onFailure {
            Log.e(TAG, "onFailure error is ${it.message}")
        }
    }

    override fun getInstallerName(): String {
        return PluginManager.PLUGIN_INSTALLER_NS
    }

    override fun install(showUI: Boolean) {
        Log.i(TAG, "start install OplusNS")

        // 1. check if allowed background install
        if (!showUI && !isInstallAllowed()) {
            return
        }
        // 2. check version
        Log.i(TAG, "local version: ${getVersion()} , remote version: ${getRemoteVersion()}")

        // 3. check if need to show UI
        if (showUI && (!UserConsentManager.isUserAllowed() || (getVersion() < 0))) {
            // user not allowed for user consent, show Dialog
            UserConfirmActivity.setUserConfirmListener(object : UserConfirmActivity.OnUserConfirmListener {
                override fun onUserConfirm(allowed: Boolean) {
                    if (allowed) {
                        Log.d(TAG, "allowed to download")
                        Oms.setNetworkStrategy(NetWorkType.NETWORK_NORMAL)
                        realInstall()
                    } else {
                        Log.d(TAG, "canceled to download")
                        cancel()
                        onStateUpdate(SplitInstallSessionStatus.CANCELED)
                    }
                }
            })
            val intent = Intent(mContext, UserConfirmActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            mContext.startActivity(intent)
            return
        }

        realInstall()
    }

    private fun realInstall() {
        mSplitInstallManager.unregisterListener(this)
        mSplitInstallManager.registerListener(this)
        mSplitInstallManager.startInstall(SplitInstallRequest.newBuilder().addModule(PluginManager.PLUGIN_NAME_NS).build()).addOnFailureListener {
            if (it is SplitInstallException
                && (it.errorCode == SplitInstallErrorCode.INCOMPATIBLE_WITH_EXISTING_SESSION)) {
                Log.w(TAG, "startInstall repeat, ignore it")
                return@addOnFailureListener
            }
            mTaskId = -1
            mProgressListeners.forEach { item ->
                item.onFailure(SplitInstallErrorCode.NETWORK_ERROR)
            }
            onStateUpdate(SplitInstallSessionStatus.FAILED)
            Log.w(TAG, "startInstall Failure is ${it.message}")
        }.addOnSuccessListener {
            mTaskId = it
            Log.d(TAG, "startInstall Prepare for success")
        }
    }

    override fun cancel() {
        if (mTaskId != -1) {
            mSplitInstallManager.cancelInstall(mTaskId)
        }
    }

    override fun unload() {
        Oms.unloadSplit(mContext, PluginManager.PLUGIN_NAME_NS)
    }

    override fun registerCallback(callback: InstallCallback) {
        mInstallCallback = callback
    }

    override fun getStatus(): Int {
        return -1
    }

    override fun getVersion(): Long {
        return Oms.getSplitVersionCode(mContext, PluginManager.PLUGIN_NAME_NS).toLong()
    }

    override fun onStateUpdate(state: SplitInstallSessionState) {
        Log.d(TAG, "state session = " + state.sessionId() + " status = " + state.status())
        when (state.status()) {
            SplitInstallSessionStatus.REQUIRES_USER_CONFIRMATION -> Log.i(TAG, "state user confirmation")

            SplitInstallSessionStatus.INSTALLED -> {
                Log.i(TAG, "state installed")

                if (mNewDownload) {
                    copyPluginModels(File(mContext.filesDir, PluginManager.NS_ENGINE_MODEL_PATH))
                    copyPluginParameter(File(mContext.filesDir, PluginManager.NS_ENGINE_PARAMETER_PATH))
                    mNewDownload = false
                }

                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.INSTALLED)
                Log.i(TAG, "installed version: ${getVersion()}")
            }

            SplitInstallSessionStatus.DOWNLOADING -> {
                Log.d(TAG, "state downloading")
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.DOWNLOADING)
                mProgressListeners.forEach {
                    it.onProgress(state.bytesDownloaded(), state.totalBytesToDownload())
                }
            }

            SplitInstallSessionStatus.DOWNLOADED -> {
                Log.i(TAG, "state downloaded")
                mNewDownload = true
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.DOWNLOADED)
                mProgressListeners.forEach {
                    it.onSuccess()
                }
            }

            SplitInstallSessionStatus.FAILED -> {
                Log.d(TAG, "state failed " + state.errorCode())
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.FAILED)
                mProgressListeners.forEach {
                    it.onFailure(state.errorCode())
                }
            }

            SplitInstallSessionStatus.CANCELED -> {
                Log.d(TAG, "state canceled")
                mInstallCallback?.onStateUpdate(SplitInstallSessionStatus.CANCELED)
            }
        }
    }

    fun onStateUpdate(status: Int) {
        mInstallCallback?.onStateUpdate(status)
    }

    override fun getClassLoader(): ClassLoader? {
        return Oms.getSplitClassLoader(PluginManager.PLUGIN_NAME_NS)
    }

    override fun needInstall(): Boolean {
        return Oms.getSplitVersionCode(mContext, PluginManager.PLUGIN_NAME_NS) < 0
    }

    private fun copyPluginModels(targetDir: File) {
        runCatching {
            val loader = getClassLoader() ?: return
            val cls = loader.loadClass(PluginManager.NS_ENGINE_CLASS_NAME)
            val instance = cls.getConstructor().newInstance()
            val method = cls.getDeclaredMethod(PluginManager.NS_ENGINE_METHOD_COPY_MODELS, Context::class.java, File::class.java)
            method.invoke(instance, mContext, targetDir)
        }.onFailure {
            Log.e(TAG, "copy models failed", it)
        }
    }

    private fun copyPluginParameter(targetDir: File) {
        runCatching {
            val loader = getClassLoader() ?: return
            val cls = loader.loadClass(PluginManager.NS_ENGINE_CLASS_NAME)
            val instance = cls.getConstructor().newInstance()
            val method = cls.getDeclaredMethod(PluginManager.NS_ENGINE_METHOD_COPY_PARAMETER, Context::class.java, File::class.java)
            method.invoke(instance, mContext, targetDir)
        }.onFailure {
            Log.e(TAG, "copy parameter failed", it)
        }
    }

    private fun getRemoteVersion(): Long {
        var result = -1L
        val info: SplitUpdateInfo? = mUpdateManager.getSplitUpdateInfo(PluginManager.PLUGIN_NAME_NS)
        info?.let {
            result = info.versionCode.toLong()
        }
        return result
    }

    private fun isInstallAllowed(): Boolean {
        var result = true

        // check auto update state
        if (UserConsentManager.waitForCheckAndInstall()) {
            return false
        }

        // check network state
        if (NetworkManager.waitForCheckAndInstall()) {
            result = false
        }
        return result
    }

    private fun setCallbacks() {
        // continue install if user consent is available
        UserConsentManager.setCallback(object : UserConsentManager.UserConsentChangedCallback {
            override fun onUserConsentChanged(isAllowed: Boolean) {
                if (isAllowed) {
                    Oms.setNetworkStrategy(NetWorkType.NETWORK_NORMAL)
                    install(false)
                } else {
                    onStateUpdate(SplitInstallSessionStatus.CANCELED)
                }
            }
        })

        // continue install if network is available
        NetworkManager.setCallback(object : NetworkManager.NetworkChangedCallback {
            override fun onNetworkChanged(available: Boolean) {
                if (available) {
                    install(false)
                }
            }
        })
    }

    private fun initDependencies(ctx: Context) {
        UserConsentManager.attachBaseContext(ctx)
        NetworkManager.attachBaseContext(ctx)
        setCallbacks()
    }
}
/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : ProgressNotificationManager.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-04
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.cloud.dialog

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.android.play.core.splitinstall.model.SplitInstallErrorCode
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.plugin.ns.cloud.CloudPluginInstaller
import kotlin.math.abs

object ProgressNotificationManager {
    private const val TAG = "PNManager"
    private const val CHANNEL_ID = "download_channel"
    private const val EXTRA_NOTIFICATION_ID = "notification_id"
    private const val NOTIFICATION_ID = 1001
    private const val MAX_PROGRESS = 100
    private const val PROGRESS_STEPS = 5
    private const val DELAY_TIME_MS = 120L
    private const val CANCEL_ACTION = "com.soundrecorder.plugin.CANCEL_NOTIFICATION"

    private lateinit var mNotificationManager: NotificationManager
    private lateinit var mChannel: NotificationChannel
    private lateinit var mCancelPendingIntent: PendingIntent
    private var mCancelReceiver: BroadcastReceiver = CancelReceiver()
    private val mHandler: Handler = Handler(Looper.getMainLooper())


    private var mProgressListener = object : CloudPluginInstaller.ProgressListener {
        var lastUpdateProgress = 0
        override fun onProgress(bytesDownloaded: Long, totalBytesToDownload: Long) {
            val newProgress = (bytesDownloaded * MAX_PROGRESS / totalBytesToDownload).toInt()
            if (abs(newProgress - lastUpdateProgress) >= PROGRESS_STEPS || newProgress == MAX_PROGRESS) {
                updateNotificationProgress(newProgress)
                lastUpdateProgress = newProgress
            }
        }

        override fun onSuccess() {
            Log.d(TAG, "onSuccess")
            completeNotification(getString(com.soundrecorder.common.R.string.noise_suppression_dialog_finish))
        }

        override fun onFailure(error: Int) {
            Log.d(TAG, "onFailure: $error")
            when (error) {
                SplitInstallErrorCode.NETWORK_ERROR -> {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(), getString(com.soundrecorder.common.R.string.noise_suppression_toast_no_network))

                    completeNotification(getString(com.soundrecorder.common.R.string.noise_suppression_toast_no_network))
                }
                SplitInstallErrorCode.INSUFFICIENT_STORAGE -> {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(), getString(com.soundrecorder.common.R.string.noise_suppression_dialog_no_space))

                    completeNotification(getString(com.soundrecorder.common.R.string.noise_suppression_dialog_no_space))
                }
                else -> completeNotification(getString(com.soundrecorder.common.R.string.noise_suppression_dialog_failed))
            }
            unregisterReceiver()
        }
    }

    fun showProgressNotification() {
        mNotificationManager = BaseApplication.getApplication().getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // create channel
        mChannel = NotificationChannel(CHANNEL_ID,
            getString(com.soundrecorder.common.R.string.app_name_main),
                NotificationManager.IMPORTANCE_HIGH)

        mNotificationManager.createNotificationChannel(mChannel)

        // create cancel action
        val cancelIntent = Intent(CANCEL_ACTION).apply {
            putExtra(EXTRA_NOTIFICATION_ID, NOTIFICATION_ID)
            setPackage(BaseApplication.getApplication().packageName)
        }

        mCancelPendingIntent = PendingIntent.getBroadcast(
            BaseApplication.getApplication(),
            NOTIFICATION_ID,
            cancelIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // create notification
        val builder: NotificationCompat.Builder = NotificationCompat.Builder(BaseApplication.getApplication(), CHANNEL_ID)
            .setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
            .setContentTitle(getString(com.soundrecorder.common.R.string.noise_suppression_dialog_downloading))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setProgress(MAX_PROGRESS, 0, false)
            .setOngoing(true)
            .addAction(com.soundrecorder.common.R.drawable.ic_launcher_recorder,
                com.soundrecorder.common.R.string.noise_suppression_bt_cancel.toString(),
                mCancelPendingIntent)

        mNotificationManager.notify(NOTIFICATION_ID, builder.build())

        CloudPluginInstaller.registerProgressListener(mProgressListener)
        registerReceiver()
    }

    private fun updateNotificationProgress(progress: Int) {
        val builder = NotificationCompat.Builder(BaseApplication.getApplication(), CHANNEL_ID)
            .setContentTitle(getString(com.soundrecorder.common.R.string.noise_suppression_dialog_downloading))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
            .setProgress(MAX_PROGRESS, progress, false)
            .addAction(
                com.soundrecorder.common.R.drawable.ic_launcher_recorder,
                getString(com.soundrecorder.common.R.string.noise_suppression_bt_cancel),
                mCancelPendingIntent
            )

        val notification = builder.build()

        mNotificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun completeNotification(content: String) {
        mHandler.postDelayed({
            val notification = NotificationCompat.Builder(BaseApplication.getApplication(), CHANNEL_ID)
                .setContentTitle(content)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
                .setProgress(0, 0, false)
                .clearActions()
                .setAutoCancel(true)
                .build()

            mNotificationManager.cancel(NOTIFICATION_ID)
            mNotificationManager.notify(NOTIFICATION_ID, notification)

            CloudPluginInstaller.unregisterProgressListener(mProgressListener)
            unregisterReceiver()
        }, DELAY_TIME_MS)
    }

    private fun getString(id: Int): String {
        return BaseApplication.getApplication().getString(id)
    }

    private class CancelReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Log.d(TAG, "receive cancel event")
            val notificationId = intent.getIntExtra(EXTRA_NOTIFICATION_ID, 0)
            if (notificationId == NOTIFICATION_ID) {
                mNotificationManager.cancel(notificationId)
                CloudPluginInstaller.cancel()
                CloudPluginInstaller.unregisterProgressListener(mProgressListener)
                unregisterReceiver()
            }
        }
    }

    private fun registerReceiver() {
        val filter = IntentFilter(CANCEL_ACTION)
        BaseApplication.getApplication().registerReceiver(mCancelReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
    }

    fun unregisterReceiver() {
        kotlin.runCatching {
            BaseApplication.getApplication().unregisterReceiver(mCancelReceiver)
        }
    }
}
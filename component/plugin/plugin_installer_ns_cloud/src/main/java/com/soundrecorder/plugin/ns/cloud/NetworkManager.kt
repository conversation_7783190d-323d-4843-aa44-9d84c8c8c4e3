/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : NetworkManager.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-02
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.cloud

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import com.soundrecorder.base.utils.NetworkUtils

object NetworkManager {
    private const val TAG = "NTManager"
    private lateinit var mContext: Application
    private lateinit var mConnectivityManager: ConnectivityManager
    private var mCallback: NetworkChangedCallback? = null

    interface NetworkChangedCallback {
        fun onNetworkChanged(available: Boolean)
    }

    fun setCallback(cb: NetworkChangedCallback) {
        mCallback = cb
    }

    fun attachBaseContext(ctx: Context) {
        mContext = ctx as Application
        mConnectivityManager = mContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    fun waitForCheckAndInstall(): Boolean {
        var result = false
        // if no network but has previous downloaded plugin, skip and use downloaded plugin
        if (NetworkUtils.isNetworkInvalid(mContext) && (CloudPluginInstaller.getVersion() < 0)) {
            // listen to network state
            Log.i(TAG, "waiting for network")
            registerNetworkChangedListener()
            result = true
        }
        return result
    }

    fun isNetworkInvalid(): Boolean {
        return NetworkUtils.isNetworkInvalid(mContext)
    }

    private val mNetworkRequest = NetworkRequest.Builder()
        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
        .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
        .build()

    private val mNetworkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onLost(network: Network) {
            Log.d(TAG, "network lost")
        }

        override fun onCapabilitiesChanged(
            network: Network,
            networkCapabilities: NetworkCapabilities
        ) {
            if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                Log.d(TAG, "network ready")
                mCallback?.onNetworkChanged(true)
                unregisterNetworkChangedListener()
            }
        }
    }

    private fun registerNetworkChangedListener() {
        mConnectivityManager.registerNetworkCallback(mNetworkRequest, mNetworkCallback)
    }

    private fun unregisterNetworkChangedListener() {
        mConnectivityManager.unregisterNetworkCallback(mNetworkCallback)
    }
}
/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : UserConfirmActivity.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-03
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.cloud.dialog

import androidx.appcompat.app.AlertDialog
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.google.android.play.core.splitinstall.model.SplitInstallErrorCode
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.plugin.ns.cloud.CloudPluginInstaller
import com.soundrecorder.plugin.ns.cloud.NetworkManager
import com.soundrecorder.plugin.ns.cloud.R
import com.soundrecorder.plugin.ns.cloud.UserConsentManager

class UserConfirmActivity : BaseActivity() {

    interface OnUserConfirmListener {
        fun onUserConfirm(allowed: Boolean)
    }

    companion object {
        fun setUserConfirmListener(listener: OnUserConfirmListener) {
            mUserConfirmListener = listener
        }

        private const val TAG = "UCActivity"
        private const val MAX_PROGRESS = 100
        private const val KEY_STATE = "key_state"
        private const val KEY_ERROR_CODE = "key_error_code"
        private var mUserConfirmListener: OnUserConfirmListener? = null
    }

    private var mUserConfirmDialog: AlertDialog? = null
    private var mProgressDialog: AlertDialog? = null
    private var mFinishedDialog: AlertDialog? = null
    private var mFailedDialog: AlertDialog? = null
    private lateinit var mProgressListener: CloudPluginInstaller.ProgressListener
    private lateinit var mHandler: Handler

    private enum class DialogState { CONFIRM, DOWNLOADING, SUCCEED, FAILED }

    private var mCurrentState = DialogState.CONFIRM
    private var mErrorCode = 0

    override fun onSaveInstanceState(out: Bundle) {
        out.putString(KEY_STATE, mCurrentState.name)
        out.putInt(KEY_ERROR_CODE, mErrorCode)
        super.onSaveInstanceState(out)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_confirm)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        mHandler = Handler(Looper.getMainLooper())

        mProgressListener = object : CloudPluginInstaller.ProgressListener {
            override fun onProgress(bytesDownloaded: Long, totalBytesToDownload: Long) {
                Log.d(TAG, "downloading $bytesDownloaded / $totalBytesToDownload")
                mHandler.post {
                    val progress = (bytesDownloaded * MAX_PROGRESS / totalBytesToDownload).toInt()
                    if (progress <= MAX_PROGRESS) {
                        mProgressDialog?.findViewById<COUIHorizontalProgressBar>(
                            com.support.dialog.R.id.progress
                        )?.progress = progress
                    }
                }
            }

            override fun onSuccess() {
                Log.d(TAG, "onSuccess")
                mHandler.post({
                    mProgressDialog?.dismiss()
                    showFinishedDialog()
                })
            }

            override fun onFailure(error: Int) {
                Log.d(TAG, "onFailure : $error")
                mHandler.post {
                    mProgressDialog?.dismiss()
                    showFailedDialog(error)
                }
            }
        }

        savedInstanceState?.let {
            mCurrentState = DialogState.valueOf(it.getString(KEY_STATE, DialogState.CONFIRM.name))
            mErrorCode = it.getInt(KEY_ERROR_CODE, 0)
        }
        when (mCurrentState) {
            DialogState.CONFIRM -> showUserConfirmDialog()
            DialogState.DOWNLOADING -> showProgressDialog()
            DialogState.SUCCEED -> showFinishedDialog()
            DialogState.FAILED -> showFailedDialog(mErrorCode)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        CloudPluginInstaller.unregisterProgressListener(mProgressListener)
    }

    private fun showUserConfirmDialog() {
        val builder = COUIAlertDialogBuilder(this, com.support.dialog.R.style.COUIAlertDialog_Bottom)
        builder.setBlurBackgroundDrawable(true)
        builder.setTitle(com.soundrecorder.common.R.string.noise_suppression_dialog_install_title)
        builder.setMessage(com.soundrecorder.common.R.string.noise_suppression_dialog_content)
        builder.setNegativeButton(com.soundrecorder.common.R.string.noise_suppression_bt_cancel) { dialog, which ->
            Log.d(TAG, "negative Confirm")
            mUserConfirmListener?.onUserConfirm(false)
            this.finish()
        }
        builder.setPositiveButton(com.soundrecorder.common.R.string.noise_suppression_bt_install, null)

        mUserConfirmDialog = builder.create()
        mUserConfirmDialog?.setCancelable(false)
        mUserConfirmDialog?.show()
        ViewUtils.updateWindowLayoutParams(mUserConfirmDialog?.window)

        mUserConfirmDialog?.getButton(AlertDialog.BUTTON_POSITIVE)?.setOnClickListener {
            Log.d(TAG, "positive Confirm")
            UserConsentManager.setUserAllowed()
            if (NetworkManager.isNetworkInvalid()) {
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(), BaseApplication.getAppContext()
                        .getString(com.soundrecorder.common.R.string.noise_suppression_toast_no_network))
            } else {
                mUserConfirmListener?.onUserConfirm(true)
                showProgressDialog()
                mUserConfirmDialog?.dismiss()
            }
        }
        mCurrentState = DialogState.CONFIRM
    }

    private fun showProgressDialog() {
        kotlin.runCatching {
            CloudPluginInstaller.registerProgressListener(mProgressListener)

            val builder = COUIAlertDialogBuilder(this, com.support.dialog.R.style.COUIAlertDialog_Progress_Cancelable)
            builder.setBlurBackgroundDrawable(true)
            builder.setTitle(com.soundrecorder.common.R.string.noise_suppression_dialog_downloading)
            builder.setNegativeButton(com.soundrecorder.common.R.string.noise_suppression_bt_cancel) { dialog, which ->
                Log.d(TAG, "negative Progress")
                mUserConfirmListener?.onUserConfirm(false)
                this.finish()
            }
            builder.setPositiveButton(com.soundrecorder.common.R.string.noise_suppression_bt_background_install) { dialog, which ->
                Log.d(TAG, "positive Progress")
                ProgressNotificationManager.showProgressNotification()
                this.finish()
            }
            mProgressDialog = builder.create()
            mProgressDialog?.setCancelable(false)
            mProgressDialog?.show()
            ViewUtils.updateWindowLayoutParams(mProgressDialog?.window)
            mCurrentState = DialogState.DOWNLOADING
        }
    }

    private fun showFinishedDialog() {
        kotlin.runCatching {
            val builder = COUIAlertDialogBuilder(this, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            builder.setBlurBackgroundDrawable(true)
            builder.setTitle(com.soundrecorder.common.R.string.noise_suppression_dialog_finish)
            builder.setNegativeButton(com.soundrecorder.common.R.string.noise_suppression_bt_got_it) { dialog, which ->
                Log.d(TAG, "ok Finished")
                this.finish()
            }

            mFinishedDialog = builder.create()
            mFinishedDialog?.setCancelable(false)
            mFinishedDialog?.show()
            ViewUtils.updateWindowLayoutParams(mFinishedDialog?.window)
            mCurrentState = DialogState.SUCCEED
        }
    }

    private fun showFailedDialog(error: Int) {
        kotlin.runCatching {
            var msgId: Int = 0
            if ((error == SplitInstallErrorCode.NETWORK_ERROR) || (error == SplitInstallErrorCode.INTERNAL_ERROR)) {
                msgId = com.soundrecorder.common.R.string.noise_suppression_toast_no_network
            } else if (error == SplitInstallErrorCode.INSUFFICIENT_STORAGE) {
                msgId = com.soundrecorder.common.R.string.noise_suppression_dialog_no_space
            }
            val builder = COUIAlertDialogBuilder(this, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            builder.setBlurBackgroundDrawable(true)
            builder.setTitle(com.soundrecorder.common.R.string.noise_suppression_dialog_failed)
            if (msgId != 0) {
                builder.setMessage(msgId)
            }
            builder.setNegativeButton(com.soundrecorder.common.R.string.noise_suppression_bt_cancel) { dialog, which ->
                Log.d(TAG, "negative Failed")
                this.finish()
            }

            builder.setPositiveButton(com.soundrecorder.common.R.string.noise_suppression_bt_retry) { dialog, which ->
                Log.d(TAG, "positive Failed")
                showUserConfirmDialog()
            }

            mFailedDialog = builder.create()
            mFailedDialog?.setCancelable(true)
            mFailedDialog?.show()
            ViewUtils.updateWindowLayoutParams(mFailedDialog?.window)
            mCurrentState = DialogState.FAILED
            mErrorCode = error
        }
    }
}
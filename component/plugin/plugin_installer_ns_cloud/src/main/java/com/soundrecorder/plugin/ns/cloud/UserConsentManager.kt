/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : UserConsentManager.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-02
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.cloud

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.preference.PreferenceManager
import android.util.Log
import com.google.android.play.core.splitinstall.model.SplitInstallSessionStatus
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.permission.PermissionUtils

object UserConsentManager {
    private const val TAG = "UCManager"
    private lateinit var mContext: Application
    private lateinit var mSharedPreferences: SharedPreferences
    private var mCallback: UserConsentChangedCallback? = null

    interface UserConsentChangedCallback {
        fun onUserConsentChanged(isAllowed: Boolean)
    }

    fun setCallback(cb: UserConsentChangedCallback) {
        mCallback = cb
    }

    fun attachBaseContext(ctx: Context) {
        mContext = ctx as Application
        mSharedPreferences = PreferenceManager.getDefaultSharedPreferences(mContext)
    }

    fun isUserAllowed(): Boolean {
        return PermissionUtils.getNSAutoUpdateState() == PermissionUtils.PERMISSION_APPLIED
    }

    fun setUserAllowed() {
        PermissionUtils.setNSAutoUpdateState(PermissionUtils.PERMISSION_APPLIED)
    }

    fun waitForCheckAndInstall(): Boolean {
        var result = false

        // check auto update state
        val autoUpdateState = PermissionUtils.getNSAutoUpdateState()
        if (autoUpdateState == PermissionUtils.PERMISSION_UNKNOWN) {
            // if previous user consent is agreed
            if (PermissionUtils.checkPermissionUpdateAlreadyApply(BaseApplication.getApplication())) {
                if (PermissionUtils.isUserStatementGranted()) {
                    Log.i(TAG, "pre user state granted")
                    setUserAllowed()
                    mCallback?.onUserConsentChanged(true)
                    result = false
                } else {
                    Log.i(TAG, "pre user state not granted")
                    PermissionUtils.setNSAutoUpdateState(PermissionUtils.PERMISSION_NOT_APPLY)
                    CloudPluginInstaller.onStateUpdate(SplitInstallSessionStatus.CANCELED)
                    result = true
                }
            } else {
                // listen to current user statement
                Log.i(TAG, "waiting for user statement")
                registerUserConsentListener()
                result = true
            }
        } else if (autoUpdateState == PermissionUtils.PERMISSION_NOT_APPLY) {
            // not allowed in background
            Log.i(TAG, "user not allowed")
            CloudPluginInstaller.onStateUpdate(SplitInstallSessionStatus.CANCELED)
            result = true
        } else if (autoUpdateState == PermissionUtils.PERMISSION_APPLIED) {
            Log.i(TAG, "user allowed")
            result = false
        }

        return result
    }

    private val listener = SharedPreferences.OnSharedPreferenceChangeListener { prefs, key ->
        when (key) {
            PermissionUtils.NOISE_SUPPRESSION_AUTO_UPDATE -> {
                val newValue = prefs.getInt(key, PermissionUtils.PERMISSION_UNKNOWN)
                Log.d(TAG, "user statement changed: $newValue")
                if (newValue == PermissionUtils.PERMISSION_APPLIED) {
                    mCallback?.onUserConsentChanged(true)
                } else {
                    mCallback?.onUserConsentChanged(false)
                }
                unregisterUserConsentListener()
            }
        }
    }

    private fun registerUserConsentListener() {
        mSharedPreferences.registerOnSharedPreferenceChangeListener(listener)
    }

    private fun unregisterUserConsentListener() {
        mSharedPreferences.unregisterOnSharedPreferenceChangeListener(listener)
    }
}
/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : LocalPluginInitializer.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-28
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.plugin.ns.cloud

import android.app.Application
import android.content.Context
import android.util.Log
import androidx.startup.Initializer
import com.soundrecorder.plugin.PluginManager

class CloudPluginInitializer : Initializer<Boolean> {
    companion object {
        const val TAG = "CPInitializer"
    }

    override fun create(context: Context): Boolean {
        Log.d(TAG, "create c plugin")
        (context as? Application)?.let {
            CloudPluginInstaller.attachBaseContext(it)
            CloudPluginInstaller.onCreate(it)
            PluginManager.registerPluginInstaller(CloudPluginInstaller.getInstallerName(), CloudPluginInstaller)
        }
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}
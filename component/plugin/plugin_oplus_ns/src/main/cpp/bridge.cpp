/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : bridge.cpp
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

#include <jni.h>
#include <string>
#include <pthread.h>
#include "LogUtil.h"
#include <stdio.h>
#include "OplusNS.h"


static opai_init_info_t getInitInfo(JNIEnv *_env, jclass _nsConfigClass, jobject _config) {
    jmethodID getSampleRateInHzMethodId = _env->GetMethodID(_nsConfigClass, "getSampleRateInHz", "()I");
    jmethodID getSampleRateOutHzMethodId = _env->GetMethodID(_nsConfigClass, "getSampleRateOutHz", "()I");
    jmethodID getMicInChannelsMethodId = _env->GetMethodID(_nsConfigClass, "getMicInChannels", "()I");
    jmethodID getRefNumChannelsMethodId = _env->GetMethodID(_nsConfigClass, "getRefNumChannels", "()I");
    jmethodID getNumOutChannelsMethodId = _env->GetMethodID(_nsConfigClass, "getNumOutChannels", "()I");
    jmethodID getBitsPerSampleMethodId = _env->GetMethodID(_nsConfigClass, "getBitsPerSample", "()I");
    jmethodID getFrameSizeMsMethodId = _env->GetMethodID(_nsConfigClass, "getFrameSizeMs", "()I");
    jmethodID getTransformTypeMethodId = _env->GetMethodID(_nsConfigClass, "getTransformType", "()I");

    jint sampleRateInHz = _env->CallIntMethod(_config, getSampleRateInHzMethodId);
    jint sampleRateOutHz = _env->CallIntMethod(_config, getSampleRateOutHzMethodId);
    jint micInChannels = _env->CallIntMethod(_config, getMicInChannelsMethodId);
    jint refNumChannels = _env->CallIntMethod(_config, getRefNumChannelsMethodId);
    jint numOutChannels = _env->CallIntMethod(_config, getNumOutChannelsMethodId);
    jint bitsPerSample = _env->CallIntMethod(_config, getBitsPerSampleMethodId);
    jint frameSizeMs = _env->CallIntMethod(_config, getFrameSizeMsMethodId);
    jint transformType = _env->CallIntMethod(_config, getTransformTypeMethodId);

    ECNS_Status nc_support = Opai_OnlyAEC;
    if (transformType == 1) {
        nc_support = Opai_AEC_NC;
    } else {
        nc_support = Opai_OnlyAEC;
    }
    LOGD("getInitInfo sampleRateInHz=%d,sampleRateOutHz=%d,micInChannels=%d,refNumChannels=%d,numOutChannels=%d,bitsPerSample=%d,frameSizeMs=%d,transformType=%d",
         sampleRateInHz,
         sampleRateOutHz,
         micInChannels,
         refNumChannels,
         numOutChannels,
         bitsPerSample,
         frameSizeMs,
         transformType);
    JavaVM *jvm = nullptr;

    auto *init_info = (opai_init_info_t *) malloc(sizeof(opai_init_info_t));
    _env->GetJavaVM((JavaVM**) &jvm);
    init_info->sample_rate_in_Hz = sampleRateInHz;
    init_info->sample_rate_out_Hz = sampleRateOutHz;
    init_info->mic_in_channels = micInChannels;
    init_info->ref_num_channels = refNumChannels;
    init_info->num_out_channels = numOutChannels;
    init_info->bits_per_sample = bitsPerSample;
    init_info->frame_size_ms = frameSizeMs;
    init_info->NC_support = nc_support;

    return {
            sampleRateInHz,
            sampleRateOutHz,
            micInChannels,
            refNumChannels,
            numOutChannels,
            bitsPerSample,
            frameSizeMs,
            nc_support
    };
}


jlong createNS(JNIEnv *_env, jclass clazz, jobject _config, jboolean enabled) {
    jclass nsConfigClass = _env->GetObjectClass(_config);
    auto initInfo = getInitInfo(_env, nsConfigClass, _config);
    jmethodID getModelPathMethodId = _env->GetMethodID(nsConfigClass, "getModelPath", "()Ljava/lang/String;");
    jmethodID getParamsPathMethodId = _env->GetMethodID(nsConfigClass, "getParamsPath", "()Ljava/lang/String;");
    auto modulePathStr = (jstring) _env->CallObjectMethod(_config, getModelPathMethodId);
    auto paramsPathStr = (jstring) _env->CallObjectMethod(_config, getParamsPathMethodId);
    char *modulePath = strdup(_env->GetStringUTFChars(modulePathStr, nullptr));
    char *paramsPath = strdup(_env->GetStringUTFChars(paramsPathStr, nullptr));


    _env->DeleteLocalRef(paramsPathStr);
    _env->DeleteLocalRef(modulePathStr);
    _env->DeleteLocalRef(nsConfigClass);

    auto *nsEngine = new OplusNSEngine();
    opai_para_info_t t_enabled;
    t_enabled.use_model = (enabled) ? Opai_Recenh_enable : Opai_Recenh_disable;
    int result = nsEngine->init_engine(initInfo, modulePath, paramsPath, t_enabled);
    LOGD("createNS result = %d , modulePath = %s , paramsPathStr = %s", result, modulePath, paramsPath);
    if (result == OPAI_SUCCESS) {
        nsEngine->get_echo_state();
        return (jlong) nsEngine;
    }
    return -1L;
}

jint setParam(JNIEnv *_env, jclass clazz, jlong engine, jstring _params_path, jboolean enabled) {
    auto *nsEngine = reinterpret_cast<OplusNSEngine *>(engine);
    char *paramsPath = strdup(_env->GetStringUTFChars(_params_path, nullptr));
    int result = -1;
    if (nsEngine && paramsPath) {
        opai_para_info_t t_enabled;
        t_enabled.use_model = (enabled) ? Opai_Recenh_enable : Opai_Recenh_disable;
        result = nsEngine->set_param(paramsPath, t_enabled);
    }
    LOGD("setParam result = %d , paramsPathStr = %s, enabled = %d", result, paramsPath, enabled);
    return result;
}

static void converseDataJ2C(JNIEnv *_env, jobject source, opai_data_t *target) {
    jclass _audioFrameClazz = _env->GetObjectClass(source);
    if (nullptr == _audioFrameClazz) {
        return;
    }
    jmethodID getDataSizeMethodId = _env->GetMethodID(_audioFrameClazz, "getDataSize", "()I");
    jmethodID getChannelsMethodId = _env->GetMethodID(_audioFrameClazz, "getChannels", "()I");
    jmethodID getDataMethodId = _env->GetMethodID(_audioFrameClazz, "getData", "()[B");

    jint dataSize = _env->CallIntMethod(source, getDataSizeMethodId);
    jint channels = _env->CallIntMethod(source, getChannelsMethodId);

    auto dataArray = (jbyteArray) _env->CallObjectMethod(source, getDataMethodId);
    // 获取数组长度
    jsize len = _env->GetArrayLength(dataArray);
    // 分配本地缓冲区
    auto *buffer = new uint8_t[len];
    // 复制数据到缓冲区
    _env->GetByteArrayRegion(dataArray, 0, len, reinterpret_cast<jbyte *>(buffer));

    target->data = buffer;
    target->channels = channels;
    target->data_bytes = dataSize;

    _env->DeleteLocalRef(_audioFrameClazz);
}

static void converseDataC2J(JNIEnv *_env, opai_data_t *source, jobject _out) {
    auto audioBuffer = (uint8_t *) source->data;
    auto channels = source->channels;
    auto dataSize = source->data_bytes;
    if (audioBuffer == nullptr || dataSize <= 0 || channels <= 0) {
        return;
    }
    jclass _audioFrameClazz = _env->GetObjectClass(_out);
    if (nullptr == _audioFrameClazz) {
        return;
    }
    jmethodID setDataSizeMethodId = _env->GetMethodID(_audioFrameClazz, "setDataSize", "(I)V");
    if (nullptr != setDataSizeMethodId) {
        _env->CallVoidMethod(_out, setDataSizeMethodId, dataSize);
    }
    jmethodID setChannelsMethodId = _env->GetMethodID(_audioFrameClazz, "setChannels", "(I)V");
    if (nullptr != setChannelsMethodId) {
        _env->CallVoidMethod(_out, setChannelsMethodId, channels);
    }

    jmethodID getDataMethodId = _env->GetMethodID(_audioFrameClazz, "getData", "()[B");
    if (getDataMethodId != nullptr) {
        auto dataArray = (jbyteArray) _env->CallObjectMethod(_out, getDataMethodId);
        if (nullptr != dataArray) {
            jsize len = _env->GetArrayLength(dataArray);
            _env->SetByteArrayRegion(dataArray, 0, len, reinterpret_cast<jbyte *>(audioBuffer));
        }
    }
    _env->DeleteLocalRef(_audioFrameClazz);
}

static void free(opai_data_t *data) {
    if (data == nullptr) {
        return;
    }
    auto *buffer = (uint8_t *) data->data;
    delete[] buffer;
    delete data;
}


jint process(JNIEnv *_env, jclass clazz, jlong engine, jobject _mic, jobject _ref, jobject _out) {
    auto *nsEngine = reinterpret_cast<OplusNSEngine *>(engine);
    jint result = -1;
    if (nsEngine) {
        auto mic = (opai_data_t *) malloc(sizeof(opai_data_t));
        if (nullptr != _mic && nullptr != mic) {
            converseDataJ2C(_env, _mic, mic);
        }
        auto ref = (opai_data_t *) malloc(sizeof(opai_data_t));
        if (nullptr != _ref && nullptr != ref) {
            converseDataJ2C(_env, _ref, ref);
        }
        auto out = (opai_data_t *) malloc(sizeof(opai_data_t));
        if (nullptr != _out && nullptr != out) {
            converseDataJ2C(_env, _out, out);
        }
        if (nullptr != mic && nullptr != ref && nullptr != out) {
            result = nsEngine->process(mic, ref, out);
            if (result == OPAI_SUCCESS) {
                converseDataC2J(_env, out, _out);
            }
        }
        free(mic);
        free(ref);
        free(out);
    }
    return result;
}

jobject getInfo(JNIEnv *_env, jclass clazz, jlong engine) {
    auto *nsEngine = reinterpret_cast<OplusNSEngine *>(engine);
    if (nsEngine) {
        jclass resultClass = _env->FindClass("com/soundrecorder/oplusns/bean/StatisticsInfo");
        jmethodID constructor = _env->GetMethodID(resultClass, "<init>", "()V");
        jobject ob = _env->NewObject(resultClass, constructor);
        if (resultClass == nullptr || constructor == nullptr || _env->ExceptionCheck()) {
            _env->ExceptionClear();
            return nullptr;
        }
        auto *info = (opai_info_t *) malloc(sizeof(opai_info_t));
        int result = nsEngine->get_info(info);
        if (result == OPAI_SUCCESS) {
            int _has_echo = info->HasEcho;
            jmethodID _setHasEcho = _env->GetMethodID(resultClass, "setHasEcho", "(I)V");
            _env->CallVoidMethod(ob, _setHasEcho, _has_echo);
        }
        _env->DeleteLocalRef(resultClass);
        return ob;
    }
    return nullptr;
}

jint getEchoState(JNIEnv *_env, jclass clazz, jlong engine) {
    auto *nsEngine = reinterpret_cast<OplusNSEngine *>(engine);
    int result = -1;
    if (nsEngine) {
        result = nsEngine->get_echo_state();
    }
    LOGD("getEchoState result = %d ", result);
    return result;
}

jint destroy(JNIEnv *_env, jclass clazz, jlong engine) {
    auto *nsEngine = reinterpret_cast<OplusNSEngine *>(engine);
    int result = -1;
    if (nsEngine) {
        result = nsEngine->destroy();
    }
    LOGD("destroy result = %d ", result);
    return result;
}

template<typename T, std::size_t sz>
static inline constexpr std::size_t NELEM(const T(&)[sz]) { return sz; }

static const JNINativeMethod NS_METHODS[] = {
        {"createNS",     "(Lcom/soundrecorder/oplusns/bean/NSConfig;Z)J",                            (jlong *) createNS},
        {"setParam",     "(JLjava/lang/String;Z)I",                                    (jint *) setParam},
        {"process",
                         "(JLcom/soundrecorder/oplusns/bean/AudioFrame;Lcom/soundrecorder/oplusns/bean/AudioFrame;Lcom/soundrecorder/oplusns/bean/AudioFrame;)I",
                                                                                      (jint *) process},
        {"getInfo",      "(J)Lcom/soundrecorder/oplusns/bean/StatisticsInfo;", (jint *) getInfo},
        {"getEchoState", "(J)I",                                                      (jint *) getEchoState},
        {"destroy",      "(J)I",                                                      (jint *) destroy}
};

JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *) {
    JniInvocation::init(vm);

    JNIEnv *env;
    if (vm->GetEnv(reinterpret_cast<void **>(&env), JNI_VERSION_1_6) != JNI_OK)
        return -1;

    jclass engineCls = env->FindClass("com/soundrecorder/oplusns/NSNative");
    if (!engineCls)
        return -1;

    if (env->RegisterNatives(
            engineCls, NS_METHODS, static_cast<jint>(NELEM(NS_METHODS))) != 0)
        return -1;

    env->DeleteLocalRef(engineCls);
    return JNI_VERSION_1_6;
}
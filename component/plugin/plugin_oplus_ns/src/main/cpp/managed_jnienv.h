/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : managed_jnienv.h
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

#ifndef SPEECHCONVERSATIONSDK_MANAGED_JNIENV_H
#define SPEECHCONVERSATIONSDK_MANAGED_JNIENV_H


#include <jni.h>

namespace JniInvocation {

    void init(JavaVM *vm);
    JavaVM *getJavaVM();
    JNIEnv *getEnv();

}


#endif //SPEECHCONVERSATIONSDK_MANAGED_JNIENV_H

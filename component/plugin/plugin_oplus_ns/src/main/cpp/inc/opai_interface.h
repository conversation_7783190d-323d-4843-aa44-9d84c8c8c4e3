#pragma once
/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : oapi_interface.h
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

#ifndef _OPAI_INTERFACE_H_
#define _OPAI_INTERFACE_H_

#include <stdint.h>

#ifndef __ANDROID__
#include <stddef.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

typedef void* opai_handle_t;

typedef enum {
    OpaiTypeAec = 0,
    OpaiTypeAecNs = 1,
} opai_type_t;

typedef void* opai_param_t;

typedef struct {
    void* data;
    size_t data_bytes;
    int channels;
} opai_data_t;

typedef struct {
    int HasEcho;
} opai_info_t;

typedef enum {
    OpaiOk = 0,
    OpaiFail = 1,
    OpaiInvalidAddr = 2,
    OpaiNullPtr = 3,
} opai_err_t;

typedef enum {
    Opai_OnlyAEC = 0,
    Opai_AEC_NC = 1,
} ECNS_Status;

typedef struct{
    int32_t sample_rate_in_Hz;     // fixed 16k
    int32_t sample_rate_out_Hz;    // fixed 16k
    int32_t mic_in_channels;       // fixed 2ch
    int32_t ref_num_channels;      // fixed 2ch
    int32_t num_out_channels;      // support 1ch  2ch, reference NC_support
    int32_t bits_per_sample;       // fixed 16bit
    int32_t frame_size_ms;         // fixed 20ms
    ECNS_Status NC_support;        // Opai_OnlyAEC:only support aec, output 2ch
} opai_init_info_t;

typedef struct {
    int32_t use_model;
} opai_para_info_t;

typedef enum {
    Opai_Recenh_disable = 0,
    Opai_Recenh_enable = 1,
} Recenh_Status;

typedef enum
{
    OPAI_SUCCESS                   = 0,    ///< Successful return
    OPAI_ALIGNMENTERROR            = 1,    ///< Memory alignment error
    OPAI_NULLADDRESS               = 2,    ///< NULL allocation address
    OPAI_OUTOFRANGE                = 3,    ///< Out of range parameter
    OPAI_INVALIDNUMSAMPLES         = 4,    ///< Invalid number of samples
    OPAI_FILE_IO_ERROR             = 5,    ///< Cant open/close/access fileio
    OPAI_MEMORY_ERROR              = 6,    ///< Memory access error
    OPAI_PARSER_INVALIDLENGTH      = 7,    ///< Parser file is wrong length
    OPAI_PARSER_INVALIDVERSION     = 8,    ///< Parser version is wrong
    OPAI_PARSER_INVALIDTYPE        = 9,    ///< Parser type is wrong
    OPAI_PARSER_INVALIDVALUE       = 10,   ///< Parser element is wrong
    OPAI_FIFO_UNDERRUN             = 11,   ///< FIFO Buffer has under run
    OPAI_INVALID_IO_PORT           = 12,   ///< Invalid port
    OPAI_INVALID_INSTANCE_STATE    = 13,   ///< Invalid instance state
    OPAI_INVALID_STATIC_STATE      = 14,   ///< Invalid static/global state
    OPAI_RETURNSTATUS_DUMMY        = 0xbeef
} opai_ret_t;


extern opai_handle_t opai_create(opai_init_info_t* init_info, char* model_path);
extern opai_ret_t opai_destroy(opai_handle_t handle);
extern opai_ret_t opai_set_param(opai_handle_t handle, char* param_path, opai_para_info_t param_info);
extern opai_ret_t opai_process(opai_handle_t handle, opai_data_t* mic, opai_data_t* ref, opai_data_t* out);
extern int opai_get_echo_state(opai_handle_t handle);
extern opai_ret_t opai_get_info(opai_handle_t handle, opai_info_t* info);

#ifdef __cplusplus
}
#endif

#endif

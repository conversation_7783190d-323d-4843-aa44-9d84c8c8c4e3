/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : OplusNS.cpp
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

#include "OplusNS.h"
#include <opai_interface.h>

using namespace std;
using namespace std::literals::string_literals;


int OplusNSEngine::init_engine(opai_init_info_t _init_info, char *_model_path, char *_params_path, opai_para_info_t enabled) {
    opai_handle_t reference = opai_create(&_init_info, _model_path);
    if (nullptr == reference) {
        LOGE("init_engine failed !!! , model_path =%s", _model_path);
        return OPAI_NULLADDRESS;
    }
    opai_ret_t result = opai_set_param(reference, _params_path, enabled);
    if (OPAI_SUCCESS == result) {
        LOGD("init_engine , opai_set_param success params_path =%s ", _params_path);
        instance = reference;
        model_path = _model_path;
        param_path = _params_path;
    } else {
        LOGE("init_engine , opai_set_param failed!!! result = %d", result);
        _release();
    }
    return result;
}

int OplusNSEngine::set_param(char *_param_path, opai_para_info_t enabled) {
    opai_ret_t result = OPAI_RETURNSTATUS_DUMMY;
    if (instance) {
        result = opai_set_param(instance, _param_path, enabled);
        if (OPAI_SUCCESS == result) {
            param_path = _param_path;
        }
    }
    LOGD("set_param , opai_set_param=%d, enabled=%d", result, enabled);
    return result;
}

int OplusNSEngine::process(opai_data_t *_mic, opai_data_t *_ref, opai_data_t *_out) {
    opai_ret_t result = OPAI_RETURNSTATUS_DUMMY;
    if (instance) {
        result = opai_process(instance, _mic, _ref, _out);
    }
    LOGD("opai_process result = %d", result);
    return result;
}



int OplusNSEngine::get_echo_state() {
    int result = -1;
    if (instance) {
        result = opai_get_echo_state(instance);
    }
    LOGD("opai_get_echo_state result = %d", result);
    return result;
}

int OplusNSEngine::get_info(opai_info_t *_info) {
    opai_ret_t result = OPAI_RETURNSTATUS_DUMMY;
    if (instance) {
        result = opai_get_info(instance, _info);
    }
    LOGD("opai_get_info result = %d", result);
    return result;
}

int OplusNSEngine::destroy() {
    return _release();
}

int OplusNSEngine::_release() {
    opai_ret_t result = opai_destroy(instance);
    LOGD("opai_destroy result = %d", result);
    if (result != OPAI_SUCCESS) {
        return result;
    }
    delete init_info;
    delete model_path;
    delete param_path;
    init_info = nullptr;
    instance = nullptr;
    model_path = nullptr;
    param_path = nullptr;
    return result;
}

OplusNSEngine::~OplusNSEngine() {
    if (instance) {
        _release();
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_oplusns_NativeLib_stringFromJNI(
        JNIEnv* env,
        jobject /* this */) {
    std::string hello = "Hello from C++";
    return env->NewStringUTF(hello.c_str());
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_soundrecorder_oplusns_NSNative_setParam(JNIEnv *env, jclass clazz, jlong engine,
                                                 jstring param_path, jboolean enabled) {
    // TODO: implement setParam()
}
/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : LogUtil.h
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

#include <android/log.h>

#ifndef HEYTAPSPEECHENGINE_LOGUTIL_H
#define HEYTAPSPEECHENGINE_LOGUT<PERSON>_H


#define LOG_TAG "oplus_ns"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG,LOG_TAG,__VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR,LOG_TAG,__VA_ARGS__)

#endif //HEYTAPSPEECHENGINE_LOGUTIL_H

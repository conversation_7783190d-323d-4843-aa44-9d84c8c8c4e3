/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : OplusNS.h
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

#ifndef SPEECHCONVERSATIONSDK_OPLUSAEC_H
#define SPEECHCONVERSATIONSDK_OPLUSAEC_H

#include <string>
#include "LogUtil.h"
#include <stdio.h>
#include <vector>
#include <map>
#include "managed_jnienv.h"

#include "inc/opai_interface.h"


class OplusNSEngine final {

    opai_handle_t instance = nullptr;

    opai_init_info_t *init_info = nullptr;

    char* model_path = nullptr;
    char* param_path = nullptr;

    OplusNSEngine(const OplusNSEngine &other) = delete;

    OplusNSEngine &operator=(const OplusNSEngine &other) = delete;

    int _release();

public:

    OplusNSEngine() = default;

    ~OplusNSEngine();

    int init_engine(opai_init_info_t _init_info, char* _model_path , char* _params_path, opai_para_info_t enabled);

    int set_param(char * param_path, opai_para_info_t enabled);

    int destroy();

    int process(opai_data_t *_mic, opai_data_t *_ref, opai_data_t *_out);

    int get_echo_state();

    int get_info(opai_info_t *_info);

};

#endif //SPEECHCONVERSATIONSDK_OPLUSAEC_H

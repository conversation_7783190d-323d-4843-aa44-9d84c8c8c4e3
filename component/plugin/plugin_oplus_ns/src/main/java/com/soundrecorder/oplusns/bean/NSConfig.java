/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : NSConfig.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.oplusns.bean;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.IntDef;
import androidx.annotation.Keep;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;


@Keep
public class NSConfig {

    private static final String TAG = "NSConfig";

    private int mSampleRateInHz;

    private int mSampleRateOutHz;

    private int mMicInChannels;

    private int mRefNumChannels;

    private int mNumOutChannels;

    private int mBitsPerSample;

    private int mFrameSizeMs;

    @TransformType
    private int mTransformType;

    private String mModelPath;

    private String mParamsPath;

    private NSConfig() {
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("AECConfig{");
        sb.append("mSampleRateInHz=").append(mSampleRateInHz);
        sb.append(", mSampleRateOutHz=").append(mSampleRateOutHz);
        sb.append(", mMicInChannels=").append(mMicInChannels);
        sb.append(", mRefNumChannels=").append(mRefNumChannels);
        sb.append(", mNumOutChannels=").append(mNumOutChannels);
        sb.append(", mBitsPerSample=").append(mBitsPerSample);
        sb.append(", mFrameSizeMs=").append(mFrameSizeMs);
        sb.append(", mTransformType=").append(mTransformType);
        sb.append(", mModelPath='").append(mModelPath).append('\'');
        sb.append(", mParamsPath='").append(mParamsPath).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public int getSampleRateInHz() {
        return mSampleRateInHz;
    }

    public int getSampleRateOutHz() {
        return mSampleRateOutHz;
    }

    public int getMicInChannels() {
        return mMicInChannels;
    }

    public int getRefNumChannels() {
        return mRefNumChannels;
    }

    public int getNumOutChannels() {
        return mNumOutChannels;
    }

    public int getBitsPerSample() {
        return mBitsPerSample;
    }

    public int getFrameSizeMs() {
        return mFrameSizeMs;
    }

    public int getTransformType() {
        return mTransformType;
    }

    public String getModelPath() {
        return mModelPath;
    }

    public String getParamsPath() {
        return mParamsPath;
    }

    public static class Builder {
        private int mSampleRateInHz = -1;

        private int mSampleRateOutHz = -1;

        private int mMicInChannels = -1;

        private int mRefNumChannels = -1;

        private int mNumOutChannels = -1;

        private int mBitsPerSample = -1;

        private int mFrameSizeMs = 20;

        @TransformType
        private int mTransformType = TransformType.ERROR;

        private String mModelPath;

        private String mParamsPath;


        public Builder setSampleRateInHz(int sampleRateInHz) {
            this.mSampleRateInHz = sampleRateInHz;
            return this;
        }

        public Builder setSampleRateOutHz(int sampleRateOutHz) {
            this.mSampleRateOutHz = sampleRateOutHz;
            return this;
        }

        public Builder setMicInChannels(int micInChannels) {
            this.mMicInChannels = micInChannels;
            return this;
        }

        public Builder setRefNumChannels(int refNumChannels) {
            this.mRefNumChannels = refNumChannels;
            return this;
        }

        public Builder setNumOutChannels(int numOutChannels) {
            this.mNumOutChannels = numOutChannels;
            return this;
        }

        public Builder setBitsPerSample(int bitsPerSample) {
            this.mBitsPerSample = bitsPerSample;
            return this;
        }

        public Builder setFrameSizeMs(int frameSizeMs) {
            this.mFrameSizeMs = frameSizeMs;
            return this;
        }

        public Builder setTransformType(@TransformType int transformType) {
            this.mTransformType = transformType;
            return this;
        }

        public Builder setModelPath(String modelPath) {
            this.mModelPath = modelPath;
            return this;
        }

        public Builder setParamsPath(String paramsPath) {
            this.mParamsPath = paramsPath;
            return this;
        }

        public NSConfig build() {
            if (mSampleRateInHz <= 0) {
                Log.e(TAG, "sampleRateInHz error");
                return null;
            }
            if (mSampleRateOutHz <= 0) {
                Log.e(TAG, "sampleRateOutHz error");
                return null;
            }
            if (mMicInChannels <= 0) {
                Log.e(TAG, "micInChannels error");
                return null;
            }
            if (mRefNumChannels <= 0) {
                Log.e(TAG, "refNumChannels error");
                return null;
            }
            if (mNumOutChannels <= 0) {
                Log.e(TAG, "numOutChannels error");
                return null;
            }
            if (mBitsPerSample <= 0) {
                Log.e(TAG, "bitsPerSample error");
                return null;
            }
            if (mFrameSizeMs <= 0) {
                Log.e(TAG, "frameSizeMs error");
                return null;
            }
            if (mTransformType < TransformType.ONLY_AEC || mTransformType > TransformType.AEC_NC) {
                Log.e(TAG, "transformType error");
                return null;
            }
            if (TextUtils.isEmpty(mModelPath)) {
                Log.e(TAG, "modelPath error");
                return null;
            }
            if (TextUtils.isEmpty(mParamsPath)) {
                Log.e(TAG, "paramsPath error");
                return null;
            }

            NSConfig config = new NSConfig();
            config.mSampleRateInHz = mSampleRateInHz;
            config.mSampleRateOutHz = mSampleRateOutHz;
            config.mMicInChannels = mMicInChannels;
            config.mRefNumChannels = mRefNumChannels;
            config.mNumOutChannels = mNumOutChannels;
            config.mBitsPerSample = mBitsPerSample;
            config.mFrameSizeMs = mFrameSizeMs;
            config.mTransformType = mTransformType;
            config.mModelPath = mModelPath;
            config.mParamsPath = mParamsPath;

            return config;
        }
    }

    @IntDef({TransformType.ONLY_AEC,
            TransformType.AEC_NC,
            TransformType.ERROR})
    @Retention(RetentionPolicy.SOURCE)
    public @interface TransformType {

        int ERROR = -1;

        /**
         * only support aec, output 2ch
         */
        int ONLY_AEC = 0;

        /**
         * support aec+nc , output 1ch
         */
        int AEC_NC = 1;
    }
}
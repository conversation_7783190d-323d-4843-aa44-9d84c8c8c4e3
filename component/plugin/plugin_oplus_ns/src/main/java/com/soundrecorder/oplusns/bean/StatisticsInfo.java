/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : StatisticsInfo.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.oplusns.bean;

import androidx.annotation.Keep;

@Keep
public class StatisticsInfo {

    public int HasEcho;

    public int getHasEcho() {
        return HasEcho;
    }

    public void setHasEcho(int hasEcho) {
        this.HasEcho = hasEcho;
    }
}

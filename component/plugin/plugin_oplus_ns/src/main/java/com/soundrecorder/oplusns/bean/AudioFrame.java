/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : AudioFrame.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.oplusns.bean;

import androidx.annotation.Keep;

@Keep
public class AudioFrame {

    private byte[] mData;

    private int mDataSize;

    private int mChannels;

    public AudioFrame() {
        this(null, 0, 0);
    }

    public AudioFrame(byte[] data, int dataSize, int channels) {
        this.mData = data;
        this.mDataSize = dataSize;
        this.mChannels = channels;
    }

    public void setData(byte[] data) {
        this.mData = data;
    }

    public void setDataSize(int dataSize) {
        this.mDataSize = dataSize;
    }

    public void setChannels(int channels) {
        this.mChannels = channels;
    }

    public byte[] getData() {
        return mData;
    }

    public int getDataSize() {
        return mDataSize;
    }

    public int getChannels() {
        return mChannels;
    }
}

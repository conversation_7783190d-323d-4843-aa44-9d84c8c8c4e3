/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : NSService.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.oplusns;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

public class NSService extends Service {
    @Override
    public IBinder onBind(Intent intent) {
        // not supported, only add for OMS server side requirement
        return null;
    }
}
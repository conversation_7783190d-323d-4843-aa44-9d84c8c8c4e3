/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : NSNative.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.oplusns;

import com.soundrecorder.oplusns.bean.AudioFrame;
import com.soundrecorder.oplusns.bean.NSConfig;
import com.soundrecorder.oplusns.bean.StatisticsInfo;

public class NSNative {

    static {
        System.loadLibrary("OplusNS");
    }

    public static native long createNS(NSConfig config, boolean enabled);

    public static native int setParam(long engine, String paramPath, boolean enabled);

    public static native int process(long engine, AudioFrame mic, AudioFrame ref, AudioFrame out);

    public static native StatisticsInfo getInfo(long engine);

    public static native int getEchoState(long engine);

    public static native int destroy(long engine);

}

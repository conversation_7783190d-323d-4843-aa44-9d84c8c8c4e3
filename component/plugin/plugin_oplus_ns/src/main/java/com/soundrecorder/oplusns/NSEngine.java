/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : NSEngine.java
 * Description :
 * Version     : 1.0
 * Date        : 2025-07-14
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.oplusns;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import androidx.annotation.NonNull;

import com.soundrecorder.oplusns.bean.AudioFrame;
import com.soundrecorder.oplusns.bean.NSConfig;
import com.soundrecorder.effect.api.Config;
import com.soundrecorder.effect.api.AudioEffect;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.atomic.AtomicBoolean;

public class NSEngine implements AudioEffect {
    private static final String TAG = "NSEngine";
    private static final String MODEL_PATH = "ns_model";
    private static final String PARAMETER_PATH = "ns_parameter";
    private static final AtomicBoolean IS_COPYING_MODEL = new AtomicBoolean(false);
    private volatile long mEngineId = -1;
    private NSConfig mNSConfig;

    @Override
    public synchronized int init(Config config, boolean enabled) {
        Log.i(TAG, "init");
        mNSConfig = new NSConfig.Builder()
                .setSampleRateInHz(config.getSampleRate())
                .setSampleRateOutHz(config.getSampleRate())
                .setMicInChannels(config.getChannels())
                .setRefNumChannels(config.getChannels())
                .setNumOutChannels(config.getChannels())
                .setBitsPerSample(config.getBitWidth())
                .setFrameSizeMs(config.getFrameSizeMs())
                .setModelPath(config.getModelPath())
                .setParamsPath(config.getParamsPath())
                .setTransformType(NSConfig.TransformType.ONLY_AEC)
                .build();
        mEngineId = NSNative.createNS(mNSConfig, enabled);
        return (int)mEngineId;
    }

    @Override
    public synchronized int process(@NonNull byte[] in, @NonNull byte[] out) {
        if (mEngineId == -1 || IS_COPYING_MODEL.get()) {
            Log.w(TAG, "skip process: " + mEngineId);
            System.arraycopy(in, 0, out, 0, in.length);
            return -1;
        }
        return NSNative.process(mEngineId, new AudioFrame(in, in.length, mNSConfig.getMicInChannels()),
                new AudioFrame(in, in.length, mNSConfig.getRefNumChannels()),
                new AudioFrame(out, out.length, mNSConfig.getNumOutChannels()));
    }

    @Override
    public synchronized void setEnabled(boolean enabled) {
        if (mEngineId == -1) {
            Log.w(TAG, "skip deInit: -1");
            return;
        }
        Log.i(TAG, "enabled: " + enabled);
        NSNative.setParam(mEngineId, mNSConfig.getParamsPath(), enabled);
    }

    @Override
    public synchronized int deInit() {
        if (mEngineId == -1) {
            Log.w(TAG, "skip deInit: -1");
            return -1;
        }
        Log.i(TAG, "deInit");
        int result = NSNative.destroy(mEngineId);
        mEngineId = -1;
        return result;
    }

    public boolean copyModelsToPathSync(Context context, File targetDir) {
        Log.i(TAG, "cp models");
        boolean result = false;
        if (IS_COPYING_MODEL.compareAndSet(false, true)) {
            AssetManager assetManager = context.getAssets();
            if (!targetDir.exists()) {
                boolean success = targetDir.mkdirs();
                if (!success) {
                    Log.e(TAG, "cannot create target dir: " + targetDir);
                    return result;
                }
            }

            String[] files = null;
            try {
                files = assetManager.list(MODEL_PATH);
            } catch (IOException e) {
                Log.e(TAG, "open asset error");
            }

            if (files != null) {
                for (String file : files) {
                    Log.d(TAG, "cp model : " + file);
                    try (
                            InputStream inputStream = assetManager.open(MODEL_PATH + File.separator + file);
                            OutputStream outputStream = new FileOutputStream(targetDir + File.separator + file)
                    ) {
                        byte[] buffer = new byte[1024];
                        int length = 0;
                        while ((length = inputStream.read(buffer)) > 0) {
                            outputStream.write(buffer, 0, length);
                        }
                    } catch (IOException e) {
                        Log.e(TAG, "cp model error: " + file, e);
                    }
                }
            }
            result = true;
            IS_COPYING_MODEL.set(false);
        }

        return result;
    }

    public boolean copyParameterToPathSync(Context context, File targetDir) {
        Log.i(TAG, "cp parameter");
        boolean result = false;
        if (IS_COPYING_MODEL.compareAndSet(false, true)) {
            AssetManager assetManager = context.getAssets();
            if (!targetDir.exists()) {
                boolean success = targetDir.mkdirs();
                if (!success) {
                    Log.e(TAG, "cannot create target dir: " + targetDir);
                    return result;
                }
            }

            String[] files = null;
            try {
                files = assetManager.list(PARAMETER_PATH);
            } catch (IOException e) {
                Log.e(TAG, "open asset error");
            }
            if (files != null) {
                for (String file : files) {
                    Log.d(TAG, "cp parameter : " + file);
                    try (
                            InputStream inputStream = assetManager.open(PARAMETER_PATH + File.separator + file);
                            OutputStream outputStream = new FileOutputStream(targetDir + File.separator + file)
                    ) {
                        byte[] buffer = new byte[1024];
                        int length = 0;
                        while ((length = inputStream.read(buffer)) > 0) {
                            outputStream.write(buffer, 0, length);
                        }
                    } catch (IOException e) {
                        Log.e(TAG, "cp model error: " + file, e);
                    }
                }
            }
            result = true;
            IS_COPYING_MODEL.set(false);
        }

        return result;
    }
}

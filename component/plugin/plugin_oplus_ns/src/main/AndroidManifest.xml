<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:dist="http://schemas.android.com/apk/distribution"
    xmlns:tools="http://schemas.android.com/tools">

    <dist:module
        dist:onDemand="true"
        dist:title="@string/noise_suppression_fun_title">
        <dist:fusing dist:include="false" />
    </dist:module>

    <application
        android:allowBackup="false">
        <service
            android:name="com.soundrecorder.oplusns.NSService"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>
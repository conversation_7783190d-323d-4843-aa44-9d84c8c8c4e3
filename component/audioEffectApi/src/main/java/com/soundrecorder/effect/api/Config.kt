/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : Config.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-25
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.effect.api

class Config private constructor(
    var sampleRate: Int,
    var channels: Int,
    var bitWidth: Int,
    var frameSizeMs: Int,
    var modelPath: String,
    var paramsPath: String
) {
    companion object {
        fun builder(): Builder {
            return Builder()
        }
    }

    class Builder {
        private var sampleRate: Int = 0
        private var channels: Int = 0
        private var bitWidth: Int = 0
        private var frameSizeMs: Int = 0
        private var modelPath: String = ""
        private var paramsPath: String = ""

        fun setSampleRate(sampleRate: Int): Builder = apply { this.sampleRate = sampleRate }
        fun setChannels(channels: Int): Builder = apply { this.channels = channels }
        fun setBitWidth(bitWidth: Int): Builder = apply { this.bitWidth = bitWidth }
        fun setFrameSizeMs(frameSizeMs: Int): Builder = apply { this.frameSizeMs = frameSizeMs }
        fun setModelPath(modelPath: String): Builder = apply { this.modelPath = modelPath }
        fun setParamsPath(paramsPath: String): Builder = apply { this.paramsPath = paramsPath }

        fun build(): Config = Config(
            sampleRate,
            channels,
            bitWidth,
            frameSizeMs,
            modelPath,
            paramsPath
        )
    }
}

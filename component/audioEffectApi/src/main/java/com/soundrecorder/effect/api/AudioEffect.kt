/*
 * *******************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File        : iAudioEffect.kt
 * Description :
 * Version     : 1.0
 * Date        : 2025-06-25
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 *  <author>                  <data>     <version>  <desc>
 *  zhangweijie                         1.0        create
 * *******************************************************************
 */

package com.soundrecorder.effect.api

interface AudioEffect {
    fun init(config: Config, enabled: Boolean): Int
    fun process(inputBuffer: Byte<PERSON>rray, outputBuffer: ByteArray): Int
    fun setEnabled(enabled: Boolean)
    fun deInit(): Int
}
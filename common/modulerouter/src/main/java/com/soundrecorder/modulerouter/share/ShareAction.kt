/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareAction
 * * Description: ShareAction
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.modulerouter.share

import android.app.Activity
import android.view.View
import kotlinx.coroutines.CoroutineScope

//未知错误
const val ERROR_CODE_UNKNOWN = 0

// 链接分享文本审核未通过
const val ERROR_CODE_CONTENT_RISK = 1
// 资源加载失败
const val ERROR_CODE_RESOURCE_LOADING_FAILED = 2

// 跳转外部应用失败
const val ERROR_CODE_REDIRECT_FAILED = 3


interface ShareAction {
    fun <T> share(
        activity: Activity?,
        shareTextContent: T,
        type: ShareType,
        coroutineScope: CoroutineScope?,
        shareListener: IShareListener?,
        jumpToNode: Boolean? = false
    )

    fun registerShareListener(listener: IShareListener)

    fun unregisterShareListener(listener: IShareListener)
    fun showShareLinkPanel(activity: Activity, link: String, anchor: View?)
    fun canUploadMoreAudioFiles(): Boolean
}
LOCAL_PATH:= $(call my-dir)

SINGLE_TARGET := NewSoundRecorder.apk

include $(<PERSON>LEAR_VARS)
LOCAL_MODULE       := $(SINGLE_TARGET)
LOCAL_MODULE_TAGS  := optional
LOCAL_MODULE_CLASS := APPS
# ifeq ($(TARGET_ARCH),arm64)
# LOCAL_PREBUILT_JNI_LIBS := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/arm64.*/.*so | sort | sed 's/^/@/' | xargs)
# LOCAL_MULTILIB := 64
# else
# LOCAL_PREBUILT_JNI_LIBS := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/armeabi.*/.*so | sort | sed 's/^/@/' | xargs)
# endif

#no odex
LOCAL_DEX_PREOPT := false

#8.0
LOCAL_CERTIFICATE  := oppo_data_app_std
# LOCAL_OPPO_THIRD_PART_APPS := true

LOCAL_SRC_FILES    := $(SINGLE_TARGET)
include $(BUILD_PREBUILT)
